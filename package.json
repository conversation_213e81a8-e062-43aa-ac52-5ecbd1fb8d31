{"name": "project-afreeserv-frontend-light", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.4.0", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "cypress:open": "cypress open", "test": "jest", "analyze": "ANALYZE=true next build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.81.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.513.0", "motion": "^12.18.1", "next": "^15.3.3", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.25.55", "zustand": "^5.0.5"}, "devDependencies": {"@cypress/react": "^9.0.1", "@eslint/eslintrc": "^3.3.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "cypress": "^14.4.1", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "30.0.0-beta.3", "lint-staged": "^16.1.0", "postcss": "^8.5.4", "prettier": "3.5.3", "tailwindcss": "^4.1.8", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}, "engines": {"node": "23.5.0"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}