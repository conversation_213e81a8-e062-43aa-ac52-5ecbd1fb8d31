import { ColumnDef } from "@tanstack/react-table";
import { TableColumnHeader } from "../table-column-header";

export const usernameColumn: ColumnDef<Service> = {
  accessorKey: "provider",
  id: "username",
  header: ({ column }) => (
    <TableColumnHeader column={column} translationKey="username" />
  ),
  cell: ({ row }) => {
    const provider = row.original.provider;
    if (!provider) return "N/A";

    const firstName = provider.firstName || "";
    const lastName = provider.lastName || "";
    return `${firstName} ${lastName}`.trim() || "N/A";
  },
};
