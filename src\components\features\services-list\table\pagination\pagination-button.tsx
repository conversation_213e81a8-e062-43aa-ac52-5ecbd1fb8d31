import { Button } from "@/components/ui/button";

interface PaginationButtonProps {
  pageNumber: number;
  isActive: boolean;
  onClick: () => void;
}

export function PaginationButton({ 
  pageNumber, 
  isActive, 
  onClick 
}: PaginationButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={`
        w-6 h-6 p-0 text-xs font-medium rounded-[5px] border
        ${
          isActive
            ? "bg-[#D4A01F] text-white border-[#D4A01F] hover:bg-[#D4A01F]"
            : "bg-white text-[#373737] border-[#EDEDED] hover:bg-gray-50"
        }
      `}
    >
      {pageNumber}
    </Button>
  );
}
