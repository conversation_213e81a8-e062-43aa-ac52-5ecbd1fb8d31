### Stage 1: Install Dependencies ###
FROM node:22-alpine AS dependencies

# Set working directory
WORKDIR /app

# Install necessary system dependencies
RUN apk add --no-cache libc6-compat

# Copy package files and install dependencies separately to leverage Docker caching
COPY package.json package-lock.json ./
RUN npm ci

### Stage 2: Build the Application ###
FROM node:22-alpine AS builder

# Set working directory
WORKDIR /app

# Copy the application code
COPY --from=dependencies /app/node_modules ./node_modules
COPY . .

# Build the Next.js application
RUN npm run build

### Stage 3: Create Lean Production Image ###
FROM node:22-alpine AS runner

# Set working directory
WORKDIR /app

# Create a non-root user for security
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
USER appuser

# Copy only the necessary built files
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/public ./public


# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=appuser:appgroup /app/.next/standalone ./
COPY --from=builder --chown=appuser:appgroup /app/.next/static ./.next/static

# Set environment variables for production
ENV NODE_ENV=production
# Disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED 1

# Expose the Next.js default port
EXPOSE 3000

# Set port env variable
ENV PORT 3000
# Set hostname to localhost
ENV HOSTNAME "0.0.0.0"

# Define the command to run the application
CMD ["node", "server.js"]
