import { servicesColumns } from "./table/services-columns";
import { ServicesDataTable } from "./table/services-data-table";

interface ServicesTableContentProps {
  services: Service[];
  totalItems: number;
  totalPages: number;
}

export function ServicesTableContent({
  services,
  totalItems,
  totalPages,
}: ServicesTableContentProps) {
  return (
    <ServicesDataTable
      columns={servicesColumns}
      data={services}
      totalItems={totalItems}
      totalPages={totalPages}
    />
  );
}
