import {
  // Service schemas
  CategorySchema,
  CreateServiceRequestSchema,
  CreateSupportRequestSchema,
  DeleteServiceParamsSchema,
  EditServiceRequestSchema,
  EditSupportRequestSchema,
  // Pagination schemas
  PaginatedResponseSchema,
  PaginationParamsSchema,
  PricingSchema,
  ProviderAddressSchema,
  // Provider schemas
  ProviderContactSchema,
  ProviderSchema,
  ProviderUnsafeMetadataSchema,
  ServiceBaseSchema,
  ServiceDetailParamsSchema,
  ServiceDetailResponseSchema,
  ServicePricingRequestSchema,
  ServicesAllParamsSchema,
  ServiceSchema,
  ServicesFiltersParamsSchema,
  // Response schemas
  ServicesListResponseSchema,
  SupportDetailParamsSchema,
  SupportDetailResponseSchema,
  SupportParamsSchema,
  // Support schemas
  SupportSchema,
  SupportsListResponseSchema,
  UserServicesParamsSchema,
  UserServicesResponseSchema,
  ValidateServiceParamsSchema,
} from "@/schemas/services";
import { z } from "zod";

// Provider types
declare type ProviderContact = z.infer<typeof ProviderContactSchema>;
declare type ProviderAddress = z.infer<typeof ProviderAddressSchema>;
declare type ProviderUnsafeMetadata = z.infer<
  typeof ProviderUnsafeMetadataSchema
>;
declare type Provider = z.infer<typeof ProviderSchema>;

// Service types
declare type Category = z.infer<typeof CategorySchema>;
declare type Pricing = z.infer<typeof PricingSchema>;
declare type ServiceBase = z.infer<typeof ServiceBaseSchema>;
declare type Service = z.infer<typeof ServiceSchema>;
declare type ServicePricingRequest = z.infer<
  typeof ServicePricingRequestSchema
>;

// Request types
declare type CreateServiceRequest = z.infer<typeof CreateServiceRequestSchema>;
declare type EditServiceRequest = z.infer<typeof EditServiceRequestSchema>;
declare type ValidateServiceParams = z.infer<
  typeof ValidateServiceParamsSchema
>;
declare type DeleteServiceParams = z.infer<typeof DeleteServiceParamsSchema>;

// Pagination types
declare type PaginatedResponse = z.infer<typeof PaginatedResponseSchema>;
declare type PaginationParams = z.infer<typeof PaginationParamsSchema>;
declare type ServicesAllParams = z.infer<typeof ServicesAllParamsSchema>;
declare type ServicesFiltersParams = z.infer<
  typeof ServicesFiltersParamsSchema
>;
declare type ServiceDetailParams = z.infer<typeof ServiceDetailParamsSchema>;
declare type UserServicesParams = z.infer<typeof UserServicesParamsSchema>;

// Support types
declare type Support = z.infer<typeof SupportSchema>;
declare type CreateSupportRequest = z.infer<typeof CreateSupportRequestSchema>;
declare type EditSupportRequest = z.infer<typeof EditSupportRequestSchema>;
declare type SupportParams = z.infer<typeof SupportParamsSchema>;
declare type SupportDetailParams = z.infer<typeof SupportDetailParamsSchema>;

// Response types
declare type ServicesListResponse = z.infer<typeof ServicesListResponseSchema>;
declare type ServiceDetailResponse = z.infer<
  typeof ServiceDetailResponseSchema
>;
declare type UserServicesResponse = z.infer<typeof UserServicesResponseSchema>;
declare type SupportDetailResponse = z.infer<
  typeof SupportDetailResponseSchema
>;
declare type SupportsListResponse = z.infer<typeof SupportsListResponseSchema>;
