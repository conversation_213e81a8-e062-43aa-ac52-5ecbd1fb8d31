# AfreeServ Frontend Light

A modern Next.js frontend application for AfreeServ, built with TypeScript and following clean code principles.

## Tech Stack

- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript 5.8.3
- **Styling**: Tailwind CSS 4.1.8
- **UI Components**: Radix UI
- **Form Handling**: React Hook Form with Zod validation
- **State Management**: React Context and Zustand for Advanced store
- **Testing**: Jest, React Testing Library, and Cypress
- **Package Manager**: pnpm 10.4.0
- **Node Version**: 23.5.0

## Prerequisites

- Node.js 23.5.0 or higher
- pnpm 10.4.0 or higher

## Getting Started

1. Install dependencies:

```bash
pnpm install
```

2.Run the development server:

```bash
pnpm dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Available Scripts

- `pnpm dev` - Start development server with Turbopack
- `pnpm build` - Build the application for production
- `pnpm start` - Start the production server
- `pnpm lint` - Run ESLint
- `pnpm test` - Run Jest tests
- `pnpm cypress:open` - Open Cypress test runner

## Project Structure

```code
├── src/                    # Source code directory
│   ├── actions/           # Server actions and API handlers
│   ├── app/              # Next.js app router pages and layouts
│   ├── components/       # Reusable UI components
│   ├── hooks/            # Custom React hooks
│   ├── i18n/             # Internationalization configuration and translations
│   ├── lib/              # Utility functions and shared logic
│   ├── schemas/          # Zod validation schemas
│   ├── services/         # API and external service integrations
│   ├── store/            # Zustand store configurations
│   ├── types/            # TypeScript type definitions
│   └── middleware.ts     # Next.js middleware configuration
│
├── cypress/              # End-to-end testing with Cypress
├── messages/             # Internationalization message files
├── .husky/              # Git hooks configuration
│
├── components.json      # UI components configuration
├── cypress.config.ts    # Cypress configuration
├── eslint.config.mjs    # ESLint configuration
├── jest.config.ts       # Jest testing configuration
├── next.config.ts       # Next.js configuration
├── package.json         # Project dependencies and scripts
├── postcss.config.mjs   # PostCSS configuration
├── tailwind.config.ts   # Tailwind CSS configuration
├── tsconfig.json        # TypeScript configuration
│
├── CHANGELOG.md         # Project changelog
├── TESTING_TOOLS.md     # Testing documentation
└── project.md           # Project documentation
```

## Development Guidelines

- Follow TypeScript best practices and maintain strict type checking
- Write clean, maintainable code following SOLID principles
- Use proper error handling and validation
- Write tests for new features and maintain existing test coverage
- Follow the established naming conventions and code style

## Testing

- Unit tests are written using Jest and React Testing Library
- E2E tests are implemented with Cypress
- Run tests with `pnpm test` for unit tests
- Run E2E tests with `pnpm cypress:open`

## Code Quality

- ESLint and Prettier are configured for code formatting
- Husky is set up for pre-commit hooks
- TypeScript strict mode is enabled
- Follow the established TypeScript guidelines in the project

## Contributing

1. Create a new branch for your feature
2. Make your changes following the established guidelines
3. Write or update tests as needed
4. Submit a pull request

## License

Private - All rights reserved
