"use client";

import { useGlobalQueryParams } from "@/hooks/use-global-query-params";

export default function PaginationUrlTest() {
  const { searchParams, setSearchParams } = useGlobalQueryParams();

  const testPageSizeChange = (size: number) => {
    console.log("Before setSearchParams:", searchParams);
    console.log("Setting limit to:", size, "next to:", 1);

    // Try different approaches
    const newParams = {
      ...searchParams,
      limit: size,
      next: 1,
    };
    console.log("New params object:", newParams);

    setSearchParams(newParams);
    console.log("After setSearchParams called");

    // Check URL after a short delay
    setTimeout(() => {
      console.log("Current URL:", window.location.href);
    }, 100);
  };

  const testPageChange = (page: number) => {
    console.log("Before page change:", searchParams);
    setSearchParams({
      ...searchParams,
      next: page,
    });
    console.log("After page change called");
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Pagination URL Test</h3>

      <div className="mb-4">
        <p>
          <strong>Current URL Parameters:</strong>
        </p>
        <pre className="bg-gray-100 p-2 rounded text-sm">
          {JSON.stringify(searchParams, null, 2)}
        </pre>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Test Page Size Changes:</h4>
          <div className="flex gap-2">
            {[10, 20, 50, 100].map((size) => (
              <button
                key={size}
                onClick={() => testPageSizeChange(size)}
                className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                {size} items
              </button>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Test Page Changes:</h4>
          <div className="flex gap-2">
            {[1, 2, 3, 4, 5].map((page) => (
              <button
                key={page}
                onClick={() => testPageChange(page)}
                className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
              >
                Page {page}
              </button>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Reset Parameters:</h4>
          <button
            onClick={() => setSearchParams({ limit: 12, next: 1 })}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Reset to defaults
          </button>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-600">
        <p>
          <strong>Expected URL format:</strong>
        </p>
        <p>
          • With params: <code>/fr/services?limit=20&next=2</code>
        </p>
        <p>• Default values should not appear in URL if they match defaults</p>
      </div>
    </div>
  );
}
