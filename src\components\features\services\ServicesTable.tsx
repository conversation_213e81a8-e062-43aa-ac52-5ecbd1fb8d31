"use client";

import { useServices } from "@/hooks/services";
import { useTranslations } from "next-intl";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

function ServicesTableSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex space-x-4">
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[300px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[150px]" />
        </div>
      ))}
    </div>
  );
}

function ErrorMessage({ error }: { error: Error }) {
  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        Failed to load services: {error.message}
      </AlertDescription>
    </Alert>
  );
}

export default function ServicesTable() {
  const { data, isLoading, error } = useServices();
  const t = useTranslations("Services");

  if (error) {
    return <ErrorMessage error={error} />;
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="mb-6">
          <Skeleton className="h-4 w-[300px]" />
        </div>
        <ServicesTableSkeleton />
      </div>
    );
  }

  const services = data?.items || [];

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <p className="text-gray-600">
          {t("pageDescription", {
            current: services.length,
            total: data?.nb_items || 0,
          })}
        </p>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("name")}</TableHead>
              <TableHead>{t("description")}</TableHead>
              <TableHead>{t("provider")}</TableHead>
              <TableHead>{t("price")}</TableHead>
              <TableHead>{t("status")}</TableHead>
              <TableHead>{t("categories")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {services.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  {t("noServices")}
                </TableCell>
              </TableRow>
            ) : (
              services.map((service) => (
                <TableRow key={service.id}>
                  <TableCell className="font-medium">{service.name}</TableCell>
                  <TableCell className="max-w-[300px] truncate">
                    {service.description}
                  </TableCell>
                  <TableCell>
                    {service.provider.firstName} {service.provider.lastName}
                  </TableCell>
                  <TableCell>
                    {service.pricing.price} {service.pricing.iso_curry}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={service.isPublished ? "default" : "secondary"}
                    >
                      {service.isPublished ? t("published") : t("draft")}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {service.categories.slice(0, 2).map((category) => (
                        <Badge
                          key={category.id}
                          variant="outline"
                          className="text-xs"
                        >
                          {category.name}
                        </Badge>
                      ))}
                      {service.categories.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{service.categories.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
