import { z } from "zod";

// Base Pagination Response Schema
export const PaginatedResponseSchema = z.object({
  nb_items: z.number(),
  next_page: z.number().nullable(),
  pagination: z.number(),
});

// Base Pagination Parameters Schema
export const PaginationParamsSchema = z.object({
  limit: z.number().optional(),
  next: z.number().optional(),
});

// Services All Parameters Schema
export const ServicesAllParamsSchema = PaginationParamsSchema.extend({
  extl_id: z.string().optional(),
});

// Services Filters Parameters Schema
export const ServicesFiltersParamsSchema = PaginationParamsSchema.extend({
  maxPrice: z.string().optional(),
  minPrice: z.string().optional(),
  order: z.enum(["recent", "old", "desc_price", "asc_price"]).optional(),
  extl_id: z.string().optional(),
  categories: z.array(z.string()).optional(),
  locations: z.array(z.string()).optional(),
});

// Service Detail Parameters Schema
export const ServiceDetailParamsSchema = z.object({
  id: z.string(),
  extl_id: z.string().optional(),
});

// User Services Parameters Schema
export const UserServicesParamsSchema = z.object({
  userExtl_id: z.string(),
});
