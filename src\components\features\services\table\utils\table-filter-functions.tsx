import { useCallback } from "react";

export const useTableFilterFunctions = () => {
  const statusFilterFn = useCallback(
    (row: any, _columnId: string, value: string) => {
      if (!value || value === "" || value === "all") return true;

      const service = row.original;
      const serviceStatus =
        service.status?.toLowerCase() ||
        (service.isPublished ? "published" : "draft");

      // Handle special case for "published" filter
      if (value.toLowerCase() === "published") {
        return (
          service.status?.toLowerCase() === "published" ||
          service.isPublished === true
        );
      }

      return serviceStatus === value.toLowerCase();
    },
    []
  );

  const dateFilterFn = useCallback(
    (row: any, columnId: string, value: string) => {
      if (!value || value === "" || value === "all") return true;

      const createdAt = row.getValue(columnId);
      if (!createdAt) return false;

      const serviceDate = new Date(createdAt);
      const now = new Date();

      switch (value) {
        case "this-year":
          return serviceDate.getFullYear() === now.getFullYear();
        case "last-year":
          return serviceDate.getFullYear() === now.getFullYear() - 1;
        case "this-month":
          return (
            serviceDate.getFullYear() === now.getFullYear() &&
            serviceDate.getMonth() === now.getMonth()
          );
        case "last-month":
          const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1);
          return (
            serviceDate.getFullYear() === lastMonth.getFullYear() &&
            serviceDate.getMonth() === lastMonth.getMonth()
          );
        default:
          return true;
      }
    },
    []
  );

  return { statusFilterFn, dateFilterFn };
};
