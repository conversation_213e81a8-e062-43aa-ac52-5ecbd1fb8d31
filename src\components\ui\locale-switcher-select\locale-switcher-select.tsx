"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components";
import { Locale, usePathname, useRouter } from "@/i18n";
import { IconChevronDown } from "@tabler/icons-react";
import Image from "next/image";
type Props = {
  defaultValue: string;
  items: Array<{ value: string; label: string; flagUrl: string }>;
  label: string;
};

export function LocaleSwitcherSelect({ defaultValue, items, label }: Props) {
  const router = useRouter();
  const pathname = usePathname();

  const onChange = (value: string) => {
    const locale = value as Locale;
    router.replace(pathname, { locale: locale });
  };

  return (
    <Select defaultValue={defaultValue} onValueChange={onChange}>
      <SelectTrigger className="w-full/80 space-x-5 ">
        <SelectValue placeholder={label} />
        <IconChevronDown className="text-input" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {items.map((item) => (
            <SelectItem
              key={item.value}
              value={item.value}
              className="flex items-center justify-between cursor-pointer py-1.5 text-base data-[highlighted]:bg-slate-100"
            >
              <div className="size-6 overflow-hidden aspect-auto object-cover rounded-full mr-9">
                <Image
                  src={item.flagUrl}
                  alt={item.label}
                  width={250}
                  height={250}
                  className="w-full h-full aspect-auto object-cover"
                />
              </div>
              <span className="text-slate-900 text-sm text-center">
                {item.label}
              </span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
