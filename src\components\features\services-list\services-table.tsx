"use client";

import { useServices } from "@/hooks/services";
import { useGlobalQueryParams } from "@/hooks/use-global-query-params";
import { ServicesTableContent } from "./services-table-content";
import { ServicesTableError } from "./services-table-error";
import { ServicesTableHeader } from "./services-table-header";
import { ServicesTableSkeleton } from "./services-table-skeleton";

export function ServicesTable() {
  const { data, isLoading, error } = useServices();
  const { searchParams } = useGlobalQueryParams();

  if (error) {
    return <ServicesTableError error={error} />;
  }

  return (
    <div>
      <ServicesTableHeader
        current={data?.items?.length || 0}
        total={data?.nb_items || 0}
      />

      {isLoading ? (
        <ServicesTableSkeleton />
      ) : (
        <ServicesTableContent
          services={data?.items || []}
          totalItems={data?.nb_items || 0}
          totalPages={Math.ceil(
            (data?.nb_items || 0) / (searchParams.limit || 10),
          )}
        />
      )}
    </div>
  );
}
