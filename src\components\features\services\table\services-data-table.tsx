"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  SortingState,
  VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";

import { Table } from "@/components/ui/table";
import { ServicesTableBody } from "./components/table-body";
import { DataTableHeader } from "./components/table-header";
import { TableFilters } from "./filters/table-filters";
import { TablePagination } from "./pagination/table-pagination";
import { useTableFilterFunctions } from "./utils/table-filter-functions";

interface ServicesDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function ServicesDataTable<TData, TValue>({
  columns,
  data,
}: ServicesDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const { statusFilterFn, dateFilterFn } = useTableFilterFunctions();

  // Apply custom filter functions to columns
  const columnsWithFilters = useMemo(() => {
    return columns.map((col: any) => {
      if (col.accessorKey === "status") {
        return { ...col, filterFn: statusFilterFn };
      }
      if (col.accessorKey === "createdAt") {
        return { ...col, filterFn: dateFilterFn };
      }
      return col;
    });
  }, [columns, statusFilterFn, dateFilterFn]);

  const table = useReactTable({
    data,
    columns: columnsWithFilters,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination,
    },
  });

  return (
    <div className="bg-white rounded-[10px] border border-secondary overflow-hidden">
      <TableFilters table={table} />

      <Table>
        <DataTableHeader table={table} />
        <ServicesTableBody table={table} columns={columns} />
      </Table>

      <TablePagination table={table} />
    </div>
  );
}
