import { Grid3X3, HomeIcon, Settings, Users } from "lucide-react";

export const navigationData: NavigationData = {
  navMain: [
    {
      titleKey: "home",
      icon: HomeIcon,
      href: "/",
      isActive: true,
    },
    {
      titleKey: "services",
      icon: Grid3X3,
      isActive: true,
      defaultOpen: true,
      items: [
        {
          titleKey: "servicesList",
          href: "/services",
          isActive: true,
        },
        {
          titleKey: "servicesDetails",
          href: "/services/details",
          isDetails: true,
        },
      ],
    },
    {
      titleKey: "support",
      icon: Users,
      items: [
        {
          titleKey: "ticketsList",
          href: "/support/tickets",
        },
        {
          titleKey: "ticketsDetails",
          href: "/support/tickets/details",
        },
      ],
    },
    {
      titleKey: "settings",
      icon: Settings,
      items: [
        {
          titleKey: "adminsList",
          href: "/settings/admins",
        },
        {
          titleKey: "adminsDetails",
          href: "/settings/admins/details",
        },
        {
          titleKey: "newAdmin",
          href: "/settings/admins/new",
        },
      ],
    },
  ],
};
