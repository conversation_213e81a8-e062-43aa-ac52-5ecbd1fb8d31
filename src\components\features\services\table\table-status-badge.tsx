import { useTranslations } from "next-intl";

interface TableStatusBadgeProps {
  status: string;
}

export function TableStatusBadge({ status }: TableStatusBadgeProps) {
  const t = useTranslations("Services");

  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case "published":
      case "publié":
        return {
          label: t("statusPublished"),
          className: "bg-green-100 text-green-800",
        };
      case "refused":
      case "refusé":
        return {
          label: t("statusRefused"),
          className: "bg-red-100 text-red-800",
        };
      case "hidden":
      case "masqué":
        return {
          label: t("statusHidden"),
          className: "bg-gray-100 text-gray-800",
        };
      case "deleted":
      case "supprimé":
        return {
          label: t("statusDeleted"),
          className: "bg-red-100 text-red-800",
        };
      case "pending":
      case "en attente":
        return {
          label: t("statusPending"),
          className: "bg-yellow-100 text-yellow-800",
        };
      case "disabled":
      case "désactivé":
        return {
          label: t("statusDisabled"),
          className: "bg-gray-100 text-gray-800",
        };
      default:
        return {
          label: t("statusUnknown"),
          className: "bg-gray-100 text-gray-800",
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}
    >
      {config.label}
    </span>
  );
}
