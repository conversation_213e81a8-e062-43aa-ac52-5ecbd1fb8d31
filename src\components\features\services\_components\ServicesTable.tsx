"use client";

import { getAllServices } from "@/features/services/services.action";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import ServicesTableSkeleton from "./ServicesTableSkeleton";
import { columns } from "./table/column";
import { DataTable } from "./table/data-table";

function ErrorMessage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-4">
      <div className="text-lg text-destructive">
        Failed to load service details
      </div>
    </div>
  );
}

export default function ServicesTable() {
  const { data, isLoading, error } = useQuery({
    queryKey: ["services"],
    queryFn: async () => await getAllServices(),
  });
  const t = useTranslations("Services");

  const services = data?.items || [];

  if (error) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-center h-32">
          <div className="text-lg text-red-600">
            Error loading services: {error.message}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <p className="text-gray-600">
          {t("pageDescription", {
            current: data?.items?.length || 0,
            total: data?.nb_items || 0,
          })}
        </p>
      </div>
      <div>
        {error ? (
          <ErrorMessage />
        ) : (
          <div>
            {isLoading ? (
              <ServicesTableSkeleton />
            ) : (
              <DataTable columns={columns} data={services} />
            )}
          </div>
        )}
      </div>
    </div>
  );
}
