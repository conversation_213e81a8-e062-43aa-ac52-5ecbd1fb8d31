import { ColumnDef } from "@tanstack/react-table";
import { actionsColumn } from "./columns/actions-column";
import { categoriesColumn } from "./columns/categories-column";
import { createdAtColumn } from "./columns/created-at-column";
import { descriptionColumn } from "./columns/description-column";
import { idColumn } from "./columns/id-column";
import { providerIdColumn } from "./columns/provider-id-column";
import { statusColumn } from "./columns/status-column";
import { usernameColumn } from "./columns/username-column";

export const servicesColumns: ColumnDef<Service>[] = [
  idColumn,
  descriptionColumn,
  categoriesColumn,
  statusColumn,
  providerIdColumn,
  usernameColumn,
  createdAtColumn,
  actionsColumn,
];
