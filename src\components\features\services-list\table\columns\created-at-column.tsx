import { ColumnDef } from "@tanstack/react-table";
import { TableColumnHeader } from "../table-column-header";
import { TableDateCell } from "../table-date-formatter";

export const createdAtColumn: ColumnDef<Service> = {
  accessorKey: "createdAt",
  enableColumnFilter: true,
  header: ({ column }) => (
    <TableColumnHeader column={column} translationKey="createdAt" />
  ),
  cell: ({ row }) => {
    return <TableDateCell dateString={row.original.createdAt} />;
  },
};
