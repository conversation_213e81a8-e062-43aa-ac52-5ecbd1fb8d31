"use client";

import { NavigationData } from "@/shared/types";
import { ChevronDown, Grid3X3, HomeIcon, Settings, Users } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import * as React from "react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { usePathname } from "next/navigation";

// Navigation data structure
export const navigationData: NavigationData = {
  navMain: [
    // ... (keep the same navigation data)
  ],
};

// Helper functions
export const usePathUtils = () => {
  const pathname = usePathname();

  const getPathWithoutLocale = (path: string) => {
    const segments = path.split("/").filter(Boolean);
    if (segments.length > 0 && ["en", "fr"].includes(segments[0])) {
      return "/" + segments.slice(1).join("/");
    }
    return path === "" ? "/" : path;
  };

  const isLinkActive = (href: string) => {
    const normalizedPathname = getPathWithoutLocale(pathname);
    return normalizedPathname === href;
  };

  const isSectionActive = (sectionName: string, href?: string) => {
    if (sectionName === "home") {
      return pathname === "/" || pathname === "/fr" || pathname === "/en";
    }
    if (href) {
      const paths = [href, `/fr${href}`, `/en${href}`];
      return paths.includes(pathname);
    }
    return (
      pathname.includes(`/${sectionName}`) ||
      pathname.includes(`/fr/${sectionName}`) ||
      pathname.includes(`/en/${sectionName}`)
    );
  };

  return { pathname, isLinkActive, isSectionActive };
};

// Logo component
export const SidebarLogo = () => {
  return (
    <SidebarHeader className="p-1.5 border-b">
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton className="hover:bg-transparent" size="lg" asChild>
            <Link href="/">
              <div className="flex items-center gap-3">
                <div className="relative w-[200] h-[80]">
                  <Image
                    src="/logo.jpg"
                    alt="Logo"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
  );
};

// Navigation Item component
export const NavigationItem = ({
  item,
  isSectionActive,
  isLinkActive,
  t,
}: {
  item: any;
  isSectionActive: (name: string, href?: string) => boolean;
  isLinkActive: (href: string) => boolean;
  t: (key: string) => string;
}) => {
  if (item.items === undefined) {
    return (
      <SidebarMenuItem>
        <SidebarMenuButton
          asChild
          className={`w-full justify-between ${
            isSectionActive(item.titleKey)
              ? "bg-sidebar-primary text-sidebar-primary-foreground"
              : ""
          }`}
        >
          <Link href={String(item.href)}>
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center size-6 rounded-lg">
                <item.icon className="size-4" />
              </div>
              <span>{t(item.titleKey)}</span>
            </div>
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    );
  }

  return (
    <Collapsible
      defaultOpen={item.defaultOpen || false}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            className={`w-full justify-between ${
              isSectionActive(item.titleKey)
                ? "bg-sidebar-primary text-sidebar-primary-foreground"
                : ""
            }`}
          >
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center size-6 rounded-lg">
                <item.icon className="size-4" />
              </div>
              <span>{t(item.titleKey)}</span>
            </div>
            <ChevronDown className="size-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        {item.items?.length && (
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items.map((subItem: any) => (
                <SidebarMenuSubItem key={subItem.titleKey}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={isLinkActive(subItem.href)}
                  >
                    {!subItem.isDetails ? (
                      <Link href={subItem.href}>{t(subItem.titleKey)}</Link>
                    ) : (
                      <span className="hover:text-sidebar-foreground">
                        {t(subItem.titleKey)}
                      </span>
                    )}
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        )}
      </SidebarMenuItem>
    </Collapsible>
  );
};

// Main AppSidebar component
export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const t = useTranslations("Navigation");
  const { isLinkActive, isSectionActive } = usePathUtils();

  return (
    <Sidebar {...props}>
      <SidebarLogo />
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu>
            {navigationData.navMain.map((item) => (
              <NavigationItem
                key={item.titleKey}
                item={item}
                isSectionActive={isSectionActive}
                isLinkActive={isLinkActive}
                t={t}
              />
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
