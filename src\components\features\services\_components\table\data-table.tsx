"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChevronRight } from "lucide-react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useTranslations } from "next-intl";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

// Helper function to generate visible pages for pagination
function getVisiblePages(currentPage: number, totalPages: number) {
  const pages = [];
  const maxVisible = 5;

  if (totalPages <= maxVisible) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    if (currentPage > 3) {
      pages.push("...");
    }

    // Show pages around current page
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);

    for (let i = start; i <= end; i++) {
      if (!pages.includes(i)) {
        pages.push(i);
      }
    }

    if (currentPage < totalPages - 2) {
      pages.push("...");
    }

    // Always show last page
    if (!pages.includes(totalPages)) {
      pages.push(totalPages);
    }
  }

  return pages;
}

export function DataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const t = useTranslations("Services");

  // Custom filter functions for TanStack Table
  const statusFilterFn = React.useCallback(
    (row: any, _columnId: string, value: string) => {
      if (!value || value === "" || value === "all") return true;

      const service = row.original;
      const serviceStatus =
        service.status?.toLowerCase() ||
        (service.isPublished ? "published" : "draft");

      // Handle special case for "published" filter
      if (value.toLowerCase() === "published") {
        return (
          service.status?.toLowerCase() === "published" ||
          service.isPublished === true
        );
      }

      return serviceStatus === value.toLowerCase();
    },
    []
  );

  const dateFilterFn = React.useCallback(
    (row: any, columnId: string, value: string) => {
      if (!value || value === "" || value === "all") return true;

      const createdAt = row.getValue(columnId);
      if (!createdAt) return false;

      const serviceDate = new Date(createdAt);
      const now = new Date();

      switch (value) {
        case "this-year":
          return serviceDate.getFullYear() === now.getFullYear();
        case "last-year":
          return serviceDate.getFullYear() === now.getFullYear() - 1;
        case "this-month":
          return (
            serviceDate.getFullYear() === now.getFullYear() &&
            serviceDate.getMonth() === now.getMonth()
          );
        case "last-month":
          const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1);
          return (
            serviceDate.getFullYear() === lastMonth.getFullYear() &&
            serviceDate.getMonth() === lastMonth.getMonth()
          );
        default:
          return true;
      }
    },
    []
  );

  // Apply custom filter functions to columns
  const columnsWithFilters = React.useMemo(() => {
    return columns.map((col: any) => {
      if (col.accessorKey === "status") {
        return { ...col, filterFn: statusFilterFn };
      }
      if (col.accessorKey === "createdAt") {
        return { ...col, filterFn: dateFilterFn };
      }
      return col;
    });
  }, [columns, statusFilterFn, dateFilterFn]);

  const table = useReactTable({
    data,
    columns: columnsWithFilters,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination,
    },
  });

  return (
    <div className="bg-white rounded-[10px] border border-secondary overflow-hidden">
      <div className="p-1">
        <div className="px-2.5 py-3">
          <div className="flex justify-between items-center gap-14">
            <Input
              placeholder={t("filterByServiceName")}
              value={
                (table.getColumn("description")?.getFilterValue() as string) ??
                ""
              }
              onChange={(event) =>
                table
                  .getColumn("description")
                  ?.setFilterValue(event.target.value)
              }
              className="max-w-sm"
            />
            {/* Spacer for alignment */}
            <div className="flex items-center gap-5">
              {/* Date Filter */}
              <div className="w-[200px] h-[35px]">
                <Select
                  onValueChange={(value) =>
                    table.getColumn("createdAt")?.setFilterValue(value)
                  }
                  value={
                    (table
                      .getColumn("createdAt")
                      ?.getFilterValue() as string) ?? "all"
                  }
                >
                  <SelectTrigger className="w-full h-full border-secondary rounded-[10px] text-xs text-[#373737] px-2.5 py-1.5">
                    <SelectValue placeholder={t("dateThisYear")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t("dateAll")}</SelectItem>
                    <SelectItem value="this-year">{t("thisYear")}</SelectItem>
                    <SelectItem value="last-year">{t("lastYear")}</SelectItem>
                    <SelectItem value="this-month">{t("thisMonth")}</SelectItem>
                    <SelectItem value="last-month">{t("lastMonth")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div className="w-[200px] h-[35px]">
                <Select
                  onValueChange={(value) =>
                    table.getColumn("status")?.setFilterValue(value)
                  }
                  value={
                    (table.getColumn("status")?.getFilterValue() as string) ??
                    "all"
                  }
                >
                  <SelectTrigger className="w-full h-full border-secondary rounded-[10px] text-xs text-[#373737] px-2.5 py-1.5">
                    <SelectValue placeholder={t("statusAll")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t("statusFilterAll")}</SelectItem>
                    <SelectItem value="published">
                      {t("statusPublished")}
                    </SelectItem>
                    <SelectItem value="refused">
                      {t("statusRefused")}
                    </SelectItem>
                    <SelectItem value="hidden">{t("statusHidden")}</SelectItem>
                    <SelectItem value="deleted">
                      {t("statusDeleted")}
                    </SelectItem>
                    <SelectItem value="pending">
                      {t("statusPending")}
                    </SelectItem>
                    <SelectItem value="disabled">
                      {t("statusDisabled")}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Display Limit */}
              <div className="w-[200px] h-[35px]">
                <Select
                  onValueChange={(value) => {
                    table.setPageSize(Number(value));
                  }}
                  value={table.getState().pagination.pageSize.toString()}
                >
                  <SelectTrigger className="w-full h-full border-secondary rounded-[10px] text-xs text-[#373737] px-2.5 py-1.5">
                    <SelectValue placeholder={t("display20")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">{t("display10")}</SelectItem>
                    <SelectItem value="20">{t("display20")}</SelectItem>
                    <SelectItem value="50">{t("display50")}</SelectItem>
                    <SelectItem value="100">{t("display100")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Table>
        <TableHeader className="bg-secondary">
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                {t("noResults")}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Custom Pagination */}
      {table.getPageCount() > 1 && (
        <div className="flex mx-auto items-center justify-center gap-3 py-2.5 px-5 w-[353px] h-[60px]">
          {/* Spacer for alignment */}
          <div className="w-6 h-6" />

          {/* Page Numbers */}
          <div className="flex items-center gap-3">
            {getVisiblePages(
              table.getState().pagination.pageIndex + 1,
              table.getPageCount()
            ).map((page, index) => {
              if (page === "...") {
                return (
                  <span
                    key={`ellipsis-${index}`}
                    className="text-[#373737] text-xs"
                  >
                    ...
                  </span>
                );
              }

              const pageNumber = page as number;
              const isActive =
                pageNumber === table.getState().pagination.pageIndex + 1;

              return (
                <Button
                  key={pageNumber}
                  variant="ghost"
                  size="sm"
                  onClick={() => table.setPageIndex(pageNumber - 1)}
                  className={`
                    w-6 h-6 p-0 text-xs font-medium rounded-[5px] border
                    ${
                      isActive
                        ? "bg-[#D4A01F] text-white border-[#D4A01F] hover:bg-[#D4A01F]"
                        : "bg-white text-[#373737] border-[#EDEDED] hover:bg-gray-50"
                    }
                  `}
                >
                  {pageNumber}
                </Button>
              );
            })}
          </div>

          {/* Next Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="w-6 h-6 p-0 bg-white border border-[#EDEDED] rounded-[5px] disabled:opacity-50"
          >
            <ChevronRight className="w-3 h-3 text-[#373737]" />
          </Button>
        </div>
      )}
    </div>
  );
}
