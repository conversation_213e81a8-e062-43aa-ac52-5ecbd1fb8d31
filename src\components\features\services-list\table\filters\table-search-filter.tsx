import { Input } from "@/components/ui/input";
import { Table } from "@tanstack/react-table";
import { useTranslations } from "next-intl";

interface TableSearchFilterProps<TData> {
  table: Table<TData>;
}

export function TableSearchFilter<TData>({
  table,
}: TableSearchFilterProps<TData>) {
  const t = useTranslations("Services");

  return (
    <Input
      placeholder={t("filterByServiceName")}
      value={(table.getColumn("description")?.getFilterValue() as string) ?? ""}
      onChange={(event) =>
        table.getColumn("description")?.setFilterValue(event.target.value)
      }
      className="max-w-sm"
    />
  );
}
