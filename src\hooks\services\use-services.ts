"use client";

import { useQuery } from "@tanstack/react-query";
import { ServicesService } from "@/services";
import { useGlobalQueryParams } from "@/hooks/use-global-query-params";
import { useMemo } from "react";

/**
 * Hook to fetch services list with URL parameter state management
 * Integrates with nuqs for automatic URL synchronization
 */
export function useServices() {
  const { searchParams } = useGlobalQueryParams();
  
  // Extract services-specific parameters from global search params
  const servicesParams: ServicesAllParams = useMemo(() => ({
    limit: searchParams.limit,
    next: searchParams.next,
    extl_id: searchParams.extl_id,
  }), [searchParams.limit, searchParams.next, searchParams.extl_id]);

  // Create query key that includes all relevant parameters
  const queryKey = useMemo(() => [
    'services',
    'list',
    servicesParams.limit,
    servicesParams.next,
    servicesParams.extl_id,
  ].filter(Boolean), [servicesParams]);

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await ServicesService.getServices(servicesParams);
      return response.response; // Extract the actual data from ApiResponse wrapper
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 3,
  });
}

/**
 * Hook to fetch a specific service by ID
 * TODO: Implement when getServiceById is ready
 */
export function useService(id: string, extl_id?: string) {
  const params: ServiceDetailParams = useMemo(() => ({
    id,
    extl_id,
  }), [id, extl_id]);

  const queryKey = useMemo(() => [
    'services',
    'detail',
    id,
    extl_id,
  ].filter(Boolean), [id, extl_id]);

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await ServicesService.getServiceById(params);
      return response.response;
    },
    enabled: !!id, // Only run query if id is provided
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 3,
  });
}

/**
 * Hook to fetch services by user ID
 * TODO: Implement when getServicesByUserId is ready
 */
export function useUserServices(userExtl_id: string) {
  const params: UserServicesParams = useMemo(() => ({
    userExtl_id,
  }), [userExtl_id]);

  const queryKey = useMemo(() => [
    'services',
    'user',
    userExtl_id,
  ], [userExtl_id]);

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await ServicesService.getServicesByUserId(params);
      return response.response;
    },
    enabled: !!userExtl_id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 3,
  });
}
