declare type ApiResponse<T> = {
  status: number;
  response: T;
};

declare type ListApiResponse<T> = {
  nb_items: number;
  next_page: number;
  pagination: number;
  items: T[];
};

/*
NAVIGATIONS TYPES
TypeScript interfaces for navigation structure
*/
declare interface NavigationSubItem {
  titleKey: string;
  href: string;
  isActive?: boolean;
  isDetails?: boolean;
}

declare interface NavigationItem {
  titleKey: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  items?: NavigationSubItem[];
  isActive?: boolean;
  defaultOpen?: boolean;
}

declare interface NavigationData {
  navMain: NavigationItem[];
}
