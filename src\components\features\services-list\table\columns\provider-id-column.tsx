import { ColumnDef } from "@tanstack/react-table";
import { TableColumnHeader } from "../table-column-header";

export const providerIdColumn: ColumnDef<Service> = {
  accessorKey: "provider.id",
  header: ({ column }) => (
    <TableColumnHeader column={column} translationKey="userId" />
  ),
  cell: ({ row }) => {
    const providerId = row.original.provider?.id;
    return providerId ? providerId.toString() : "N/A";
  },
};
