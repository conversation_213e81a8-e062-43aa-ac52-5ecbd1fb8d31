import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Table } from "@tanstack/react-table";
import { useTranslations } from "next-intl";

interface TableStatusFilterProps<TData> {
  table: Table<TData>;
}

export function TableStatusFilter<TData>({ table }: TableStatusFilterProps<TData>) {
  const t = useTranslations("Services");

  return (
    <div className="w-[200px] h-[35px]">
      <Select
        onValueChange={(value) =>
          table.getColumn("status")?.setFilterValue(value)
        }
        value={
          (table.getColumn("status")?.getFilterValue() as string) ?? "all"
        }
      >
        <SelectTrigger className="w-full h-full border-secondary rounded-[10px] text-xs text-[#373737] px-2.5 py-1.5">
          <SelectValue placeholder={t("statusAll")} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t("statusFilterAll")}</SelectItem>
          <SelectItem value="published">{t("statusPublished")}</SelectItem>
          <SelectItem value="refused">{t("statusRefused")}</SelectItem>
          <SelectItem value="hidden">{t("statusHidden")}</SelectItem>
          <SelectItem value="deleted">{t("statusDeleted")}</SelectItem>
          <SelectItem value="pending">{t("statusPending")}</SelectItem>
          <SelectItem value="disabled">{t("statusDisabled")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
