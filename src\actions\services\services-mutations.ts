"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ServicesService } from "@/services";
import { toast } from "sonner"; // Assuming you're using sonner for notifications

/**
 * Mutation hook for creating a new service
 * TODO: Implement when createService method is ready
 */
export function useCreateService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateServiceRequest) => {
      const response = await ServicesService.createService(data);
      return response.response;
    },
    onSuccess: (data) => {
      // Invalidate and refetch services list
      queryClient.invalidateQueries({ queryKey: ["services", "list"] });

      // Optionally invalidate user services if applicable
      if (data.provider?.extl_id) {
        queryClient.invalidateQueries({
          queryKey: ["services", "user", data.provider.extl_id],
        });
      }

      toast.success("Service created successfully!");
    },
    onError: (error) => {
      console.error("Failed to create service:", error);
      toast.error("Failed to create service. Please try again.");
    },
  });
}

/**
 * Mutation hook for updating an existing service
 * TODO: Implement when updateService method is ready
 */
export function useUpdateService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: EditServiceRequest) => {
      const response = await ServicesService.updateService(data);
      return response.response;
    },
    onSuccess: (data) => {
      // Invalidate services list
      queryClient.invalidateQueries({ queryKey: ["services", "list"] });

      // Invalidate specific service detail
      queryClient.invalidateQueries({
        queryKey: ["services", "detail", data.id.toString()],
      });

      // Invalidate user services if applicable
      if (data.provider?.extl_id) {
        queryClient.invalidateQueries({
          queryKey: ["services", "user", data.provider.extl_id],
        });
      }

      toast.success("Service updated successfully!");
    },
    onError: (error) => {
      console.error("Failed to update service:", error);
      toast.error("Failed to update service. Please try again.");
    },
  });
}

/**
 * Mutation hook for deleting a service
 * TODO: Implement when deleteService method is ready
 */
export function useDeleteService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: DeleteServiceParams) => {
      const response = await ServicesService.deleteService(params);
      return response.response;
    },
    onSuccess: (_, variables) => {
      // Invalidate services list
      queryClient.invalidateQueries({ queryKey: ["services", "list"] });

      // Remove specific service from cache
      queryClient.removeQueries({
        queryKey: ["services", "detail", variables.id.toString()],
      });

      // Invalidate user services (we don't have user info here, so invalidate all)
      queryClient.invalidateQueries({ queryKey: ["services", "user"] });

      toast.success("Service deleted successfully!");
    },
    onError: (error) => {
      console.error("Failed to delete service:", error);
      toast.error("Failed to delete service. Please try again.");
    },
  });
}

/**
 * Mutation hook for validating/publishing a service
 * TODO: Implement when validateService method is ready
 */
export function useValidateService() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: ValidateServiceParams) => {
      const response = await ServicesService.validateService(params);
      return response.response;
    },
    onSuccess: (_, variables) => {
      // Invalidate services list to reflect status change
      queryClient.invalidateQueries({ queryKey: ["services", "list"] });

      // Invalidate specific service detail
      queryClient.invalidateQueries({
        queryKey: ["services", "detail", variables.serviceId.toString()],
      });

      const action = variables.isPublished ? "published" : "unpublished";
      toast.success(`Service ${action} successfully!`);
    },
    onError: (error) => {
      console.error("Failed to validate service:", error);
      toast.error("Failed to update service status. Please try again.");
    },
  });
}
