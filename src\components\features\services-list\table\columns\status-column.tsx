import { ColumnDef } from "@tanstack/react-table";
import { TableColumnHeader } from "../table-column-header";
import { TableStatusBadge } from "../table-status-badge";

export const statusColumn: ColumnDef<Service> = {
  accessorKey: "status",
  enableColumnFilter: true,
  header: ({ column }) => (
    <TableColumnHeader column={column} translationKey="status" />
  ),
  cell: ({ row }) => {
    const status = row.original.isPublished ? "published" : "draft";
    return <TableStatusBadge status={status} />;
  },
};
