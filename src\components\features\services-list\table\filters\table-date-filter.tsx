import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Table } from "@tanstack/react-table";
import { ChevronDownIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface TableDateFilterProps<TData> {
  table: Table<TData>;
}

export function TableDateFilter<TData>({ table }: TableDateFilterProps<TData>) {
  const t = useTranslations("Services");

  return (
    <div>
      <Select
        onValueChange={(value) =>
          table
            .getColumn("createdAt")
            ?.setFilterValue(value === "all" ? "" : value)
        }
        value={
          (table.getColumn("createdAt")?.getFilterValue() as string) ?? "all"
        }
      >
        <SelectTrigger className=" text-foreground w-full border-input rounded-md bg-background focus:ring-2 focus:ring-ring focus:ring-offset-2">
          <SelectValue placeholder={t("dateThisYear")} />
          <ChevronDownIcon className="text-input" />{" "}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t("dateAll")}</SelectItem>
          <SelectItem value="this-year">{t("thisYear")}</SelectItem>
          <SelectItem value="last-year">{t("lastYear")}</SelectItem>
          <SelectItem value="this-month">{t("thisMonth")}</SelectItem>
          <SelectItem value="last-month">{t("lastMonth")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
