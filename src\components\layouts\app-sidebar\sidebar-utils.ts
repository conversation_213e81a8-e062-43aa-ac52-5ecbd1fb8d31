import { usePathname } from "next/navigation";

export const usePathUtils = () => {
  const pathname = usePathname();

  const getPathWithoutLocale = (path: string) => {
    const segments = path.split("/").filter(Boolean);
    if (segments.length > 0 && ["en", "fr"].includes(segments[0])) {
      return "/" + segments.slice(1).join("/");
    }
    return path === "" ? "/" : path;
  };

  const isLinkActive = (href: string) => {
    const normalizedPathname = getPathWithoutLocale(pathname);
    return normalizedPathname === href;
  };

  const isSectionActive = (sectionName: string, href?: string) => {
    if (sectionName === "home") {
      return pathname === "/" || pathname === "/fr" || pathname === "/en";
    }
    if (href) {
      const paths = [href, `/fr${href}`, `/en${href}`];
      return paths.includes(pathname);
    }
    return (
      pathname.includes(`/${sectionName}`) ||
      pathname.includes(`/fr/${sectionName}`) ||
      pathname.includes(`/en/${sectionName}`)
    );
  };

  return { pathname, isLinkActive, isSectionActive };
};
