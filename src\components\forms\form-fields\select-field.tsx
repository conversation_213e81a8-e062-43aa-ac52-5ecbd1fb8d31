"use client";

import { Check, ChevronsUpDown } from "lucide-react";
import {
  Control,
  FieldValues,
  Path,
  ControllerRenderProps,
} from "react-hook-form";

import { cn } from "@/lib";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  Button,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
  ButtonProps,
} from "@/components";
import { ReactNode } from "react";
import { IconX } from "@tabler/icons-react";

interface SelectOptionsType {
  label: string;
  value: string;
}

interface FieldIconProps {
  icon: ReactNode;
  position: "start" | "end";
}

interface SelectField<T extends FieldValues> extends ButtonProps {
  control: Control<T>;
  fieldName: Path<T>;
  label?: string;
  description?: string;
  options: SelectOptionsType[];
  placeholder?: string;
  searchPlaceholder?: string;
  notFoundMessage?: string;
  useSearchField?: boolean;
  icon?: FieldIconProps;
}

function SelectButtonContent<T extends FieldValues>({
  field,
  options,
  placeholder,
  icon,
}: {
  field: ControllerRenderProps<T, Path<T>>;
  options: SelectOptionsType[];
  placeholder?: string;
  icon?: FieldIconProps;
}) {
  return (
    <>
      <div className="flex items-center gap-1.5">
        {icon && icon.position === "start" && icon.icon}
        {field.value
          ? options.find((option) => option.value === field.value)?.label
          : placeholder}
      </div>
      {field.value && (
        <span
          className="absolute right-7 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent cursor-pointer"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            field.onChange(undefined);
          }}
        >
          <IconX className="size-4 shrink-0 opacity-50" />
        </span>
      )}
      {icon && icon.position === "end" ? (
        icon.icon
      ) : (
        <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
      )}
    </>
  );
}

function SelectOptions<T extends FieldValues>({
  options,
  field,
  useSearchField,
  searchPlaceholder,
  notFoundMessage,
}: {
  options: SelectOptionsType[];
  field: ControllerRenderProps<T, Path<T>>;
  useSearchField?: boolean;
  searchPlaceholder?: string;
  notFoundMessage?: string;
}) {
  return (
    <Command>
      {useSearchField && <CommandInput placeholder={searchPlaceholder} />}
      <CommandList>
        <CommandEmpty>{notFoundMessage}</CommandEmpty>
        <CommandGroup>
          {options.map((option) => (
            <CommandItem
              value={option.label}
              key={option.value}
              onSelect={() => {
                field.onChange(option.value);
              }}
            >
              {option.label}
              <Check
                className={cn(
                  "ml-auto",
                  option.value === field.value ? "opacity-100" : "opacity-0",
                )}
              />
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </Command>
  );
}

export function SelectField<T extends FieldValues>(props: SelectField<T>) {
  const {
    control,
    fieldName,
    label,
    options,
    placeholder,
    searchPlaceholder,
    useSearchField = true,
    notFoundMessage,
    className,
    icon,
    ...rest
  } = props;

  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          {label ? <FormLabel>{label}</FormLabel> : null}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                className={cn(
                  "relative w-full justify-between text-base font-normal placeholder:font-normal border-input h-10",
                  !field.value && "text-muted-foreground font-normal",
                  className,
                )}
                {...rest}
              >
                <SelectButtonContent
                  field={field}
                  options={options}
                  placeholder={placeholder}
                  icon={icon}
                />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="!w-full p-0" align="start">
              <SelectOptions
                options={options}
                field={field}
                useSearchField={useSearchField}
                searchPlaceholder={searchPlaceholder}
                notFoundMessage={notFoundMessage}
              />
            </PopoverContent>
          </Popover>
          <FormMessage useTranslationKey={true} />
        </FormItem>
      )}
    />
  );
}
