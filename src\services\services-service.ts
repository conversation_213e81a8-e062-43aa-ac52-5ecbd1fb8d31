import { ServicesListResponseSchema } from "@/schemas/services";
import { BaseApiService } from "./base-service";

export class ServicesServiceClass extends BaseApiService {
  private BASE_SERVICES_URL = "services/";

  constructor() {
    super();
    this.getServices = this.getServices.bind(this);
  }

  /**
   * Get all services with optional parameters
   * @param params - Optional parameters for filtering services
   * @returns Promise<ApiResponse<ServicesListResponse>>
   */
  async getServices(params: ServicesAllParams = {}) {
    const { limit = 5000, next = 1, extl_id } = params;

    // Build query parameters for the API request
    const queryParams: Record<string, string | number> = {
      limit,
      next,
    };

    if (extl_id) {
      queryParams.extl_id = extl_id;
    }

    return await this.request(
      `${this.BASE_SERVICES_URL}all/${limit}/${next}`,
      {
        method: "GET",
        query: queryParams,
        tag: `services-all-${limit}-${next}${extl_id ? `-${extl_id}` : ""}`,
      },
      ServicesListResponseSchema,
    );
  }

  // TODO: Implement getServiceById method
  async getServiceById(_params: ServiceDetailParams) {
    // TODO: Implement service detail fetching
    throw new Error("Method not implemented yet");
  }

  // TODO: Implement getServicesByUserId method
  async getServicesByUserId(_params: UserServicesParams) {
    // TODO: Implement user services fetching
    throw new Error("Method not implemented yet");
  }

  // TODO: Implement createService method
  async createService(_data: CreateServiceRequest) {
    // TODO: Implement service creation
    throw new Error("Method not implemented yet");
  }

  // TODO: Implement updateService method
  async updateService(_data: EditServiceRequest) {
    // TODO: Implement service update
    throw new Error("Method not implemented yet");
  }

  // TODO: Implement deleteService method
  async deleteService(_params: DeleteServiceParams) {
    // TODO: Implement service deletion
    throw new Error("Method not implemented yet");
  }

  // TODO: Implement validateService method
  async validateService(_params: ValidateServiceParams) {
    // TODO: Implement service validation
    throw new Error("Method not implemented yet");
  }
}

export const ServicesService = new ServicesServiceClass();
