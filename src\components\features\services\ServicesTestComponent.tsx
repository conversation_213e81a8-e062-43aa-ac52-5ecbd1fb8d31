"use client";

import { useServices } from "@/hooks/services";

export default function ServicesTestComponent() {
  const { data, isLoading, error } = useServices();

  if (isLoading) {
    return <div>Loading services...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Services Test</h2>
      <p>Total services: {data?.nb_items || 0}</p>
      <p>Current page items: {data?.items?.length || 0}</p>

      {data?.items && data.items.length > 0 && (
        <div className="mt-4">
          <h3 className="font-semibold">First service:</h3>
          <pre className="bg-gray-100 p-2 rounded text-sm">
            {JSON.stringify(data.items[0], null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
