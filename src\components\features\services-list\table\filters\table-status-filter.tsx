import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Table } from "@tanstack/react-table";
import { ChevronDownIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface TableStatusFilterProps<TData> {
  table: Table<TData>;
}

export function TableStatusFilter<TData>({
  table,
}: TableStatusFilterProps<TData>) {
  const t = useTranslations("Services");

  return (
    <div>
      <Select
        onValueChange={(value) =>
          table.getColumn("status")?.setFilterValue(value)
        }
        value={(table.getColumn("status")?.getFilterValue() as string) ?? "all"}
      >
        <SelectTrigger className="w-full border-input rounded-md bg-background focus:ring-2 focus:ring-ring focus:ring-offset-2">
          <SelectValue placeholder={t("statusAll")} />
          <ChevronDownIcon className="text-input" />{" "}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t("statusFilterAll")}</SelectItem>
          <SelectItem value="published">{t("statusPublished")}</SelectItem>
          <SelectItem value="refused">{t("statusRefused")}</SelectItem>
          <SelectItem value="hidden">{t("statusHidden")}</SelectItem>
          <SelectItem value="deleted">{t("statusDeleted")}</SelectItem>
          <SelectItem value="pending">{t("statusPending")}</SelectItem>
          <SelectItem value="disabled">{t("statusDisabled")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
