"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";

import { Table } from "@/components/ui/table";
import { useGlobalQueryParams } from "@/hooks";
import { ServicesTableBody } from "./components/table-body";
import { DataTableHeader } from "./components/table-header";
import { TableFilters } from "./filters/table-filters";
import { TablePagination } from "./pagination/table-pagination";
import { useTableFilterFunctions } from "./utils/table-filter-functions";

interface ServicesDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  totalItems: number;
  totalPages: number;
}

export function ServicesDataTable<TData, TValue>({
  columns,
  data,
  totalPages,
}: ServicesDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const { searchParams } = useGlobalQueryParams();

  const { statusFilterFn, dateFilterFn } = useTableFilterFunctions();

  // Apply custom filter functions to columns
  const columnsWithFilters = useMemo(() => {
    return columns.map((col: any) => {
      if (col.accessorKey === "status") {
        return { ...col, filterFn: statusFilterFn };
      }
      if (col.accessorKey === "createdAt") {
        return { ...col, filterFn: dateFilterFn };
      }
      return col;
    });
  }, [columns, statusFilterFn, dateFilterFn]);

  const table = useReactTable({
    data,
    columns: columnsWithFilters,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    manualPagination: true,
    pageCount: totalPages,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination: {
        pageIndex: (searchParams.next || 1) - 1, // Convert to 0-based index
        pageSize: searchParams.limit || 10,
      },
    },
  });

  return (
    <div className="bg-white rounded-[10px] border border-secondary overflow-hidden">
      <TableFilters table={table} />

      <Table>
        <DataTableHeader table={table} />
        <ServicesTableBody table={table} columns={columns} />
      </Table>

      <TablePagination table={table} />
    </div>
  );
}
