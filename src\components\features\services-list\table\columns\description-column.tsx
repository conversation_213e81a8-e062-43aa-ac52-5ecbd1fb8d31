import { ColumnDef } from "@tanstack/react-table";
import { TableColumnHeader } from "../table-column-header";
import React from "react";

export const descriptionColumn: ColumnDef<Service> = {
  accessorKey: "description",
  header: ({ column }) => (
    <TableColumnHeader column={column} translationKey="serviceTitle" />
  ),
  cell: ({ row }) => {
    const description = row.getValue("description") as string;
    return <div className="truncate hover:line-clamp-none">{description}</div>;
  },
};
