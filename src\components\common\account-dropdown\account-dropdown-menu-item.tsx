import { DropdownMenuItem } from "@/components/ui";

type MenuItemProps = {
  onClick?: () => void;
  icon: React.ReactNode;
  label: string;
};

export function MenuItem({ onClick, icon, label }: MenuItemProps) {
  return (
    <DropdownMenuItem
      onClick={onClick}
      className="text-sm font-medium text-foreground px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
    >
      {icon}
      {label}
    </DropdownMenuItem>
  );
}
