# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),  
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html)

---

## [0.2.0] - 2025-07-8

### Added

- **Services Architecture Implementation (Step 1):**
  Complete implementation of the services layer following Clean Architecture patterns with proper separation of concerns.

- **Modular Schema Structure:**

  - Decomposed monolithic services schema into focused, maintainable files:
    - `provider-schema.ts` - Provider-related schemas and validations
    - `service-schema.ts` - Service entities, categories, and pricing schemas
    - `pagination-schema.ts` - Pagination parameters and response schemas
    - `support-schema.ts` - Support ticket and request schemas
    - `services-list-response-schema.ts` - API response schemas
  - Centralized exports using `export *` pattern in `index.ts`

- **Global TypeScript Type System:**

  - Implemented proper global type declarations using `declare global` pattern
  - All service-related types available globally without imports
  - Type-safe integration with Zod schemas using `z.infer<typeof Schema>`
  - Resolved TypeScript module resolution issues for seamless development

- **Client-Side Compatible API Service:**

  - Created `BaseApiService` class extending existing patterns
  - Client-side token management using localStorage
  - Proper server/client component separation for Next.js App Router
  - Comprehensive error handling with custom `ApiError` class
  - URL construction with query parameter support

- **ServicesService Implementation:**

  - `getServices()` method with proper parameter handling
  - URL construction following API specification: `/services/all/{limit}/{next}`
  - Optional `extl_id` query parameter support
  - Schema validation using `ServicesListResponseSchema`
  - TODO placeholders for future methods (getServiceById, createService, etc.)

- **React Query Integration:**

  - `useServices` hook with TanStack Query for optimized data fetching
  - Automatic URL parameter state management using nuqs integration
  - Intelligent caching with proper query keys and stale time configuration
  - Background refetching and error retry mechanisms
  - Placeholder hooks for `useService` and `useUserServices`

- **Mutation Actions with TanStack Query:**

  - Comprehensive mutation hooks: `useCreateService`, `useUpdateService`, `useDeleteService`, `useValidateService`
  - Automatic cache invalidation on successful mutations
  - User feedback with toast notifications
  - Proper error handling with user-friendly messages
  - Optimistic updates and rollback capabilities

- **Modern UI Components:**
  - `ServicesTable` component with loading states and error handling
  - Integration with shadcn/ui components (Table, Badge, Skeleton, Alert)
  - Responsive design with proper data display
  - Internationalization support with next-intl
  - Test components for development and debugging

### Fixed

- **Server/Client Component Compatibility:**

  - Resolved `next/headers` import error in client-side components
  - Separated server-side and client-side concerns properly
  - Fixed font loading issues with Google Fonts and Turbopack
  - Eliminated server-only API usage in client components

- **URL Construction Issues:**

  - Fixed duplicate query parameters in API requests
  - Corrected API endpoint construction: `/services/all/{limit}/{next}`
  - Proper handling of optional `extl_id` parameter
  - Removed redundant query parameters from URL path

- **TypeScript Global Types:**
  - Fixed global type declaration pattern using `declare global`
  - Resolved module resolution issues for service types
  - Proper integration with existing global type system

### Changed

- **Architecture Alignment:**

  - Refactored existing implementation to follow established project patterns
  - Aligned with Clean Architecture principles and file organization
  - Updated service layer to extend `BaseApiService` properly
  - Improved error handling and response processing

- **Development Experience:**
  - Enhanced debugging capabilities with test components
  - Better error messages and logging for development
  - Improved TypeScript intellisense and type safety
  - Streamlined development workflow with proper tooling

### Technical Details

- **File Structure:** Follows established patterns with max 100-130 lines per file
- **Type Safety:** Full TypeScript coverage with global type declarations
- **Caching Strategy:** Optimized with TanStack Query for performance
- **URL Management:** nuqs integration for automatic URL state synchronization
- **Error Handling:** Comprehensive error boundaries and user feedback
- **Testing:** Test components and debugging tools for development

### Next Steps

- **Step 2:** Implement comprehensive UI components for services management
- **Step 3:** Add filtering, sorting, and search functionality
- **Step 4:** Implement remaining service methods (CRUD operations)

---

## [0.1.0] - 2024-07-8

### Added

- **Modern Next.js 15+ project foundation:**  
  Established a robust project base using Next.js 15+ with the App Router, TypeScript, and a modular directory structure to support scalable development.

- **Internationalization (i18n) support:**  
  Integrated next-intl for seamless multi-language support, including configuration, routing, and locale management.

- **UI component system:**  
  Set up a comprehensive UI library using shadcn/ui and Radix UI primitives, providing reusable, accessible components for rapid interface development.

- **Form management and validation:**  
  Implemented react-hook-form for efficient form state management and zod for schema-based validation, ensuring reliable user input handling.

- **State management architecture:**  
  Introduced a type-safe global state management solution within the `src/store/` directory, enabling predictable and maintainable state handling.

- **Testing infrastructure:**  
  Configured Jest for unit testing and Cypress for end-to-end testing, with supporting documentation in `TESTING_TOOLS.md` to guide contributors.

- **Development tooling and automation:**

  - ESLint and Prettier for code quality and formatting consistency.
  - Husky and lint-staged for automated pre-commit checks.
  - Tailwind CSS and PostCSS for utility-first styling and CSS processing.
  - pnpm as the preferred package manager for efficient dependency management.

- **Comprehensive documentation:**

  - `README.md` with setup, development, and contribution guidelines.
  - `project.md` outlining project structure, conventions, and architectural decisions.
  - Example files and directories to illustrate best practices for data fetching, state management, and service layer design.

- **Utility and support modules:**
  - Custom hooks in `src/hooks/` for common React patterns.
  - Validation schemas in `src/schemas/`.
  - Service layer abstraction in `src/services/`.
  - Type definitions in `src/types/`.
  - Shared utilities in `src/lib/`.

### Changed

- **Project structure enhancements:**

  - Adopted a clean, modular architecture with clear separation of concerns.
  - Organized source code into domain-specific directories for maintainability and scalability.
  - Updated TypeScript configuration for strict type safety and improved developer experience.

- **Tooling and configuration improvements:**
  - Migrated from npm to pnpm for faster, more reliable dependency management.
  - Upgraded Node.js version requirement to 23.5.0 to leverage the latest features and performance improvements.

### Removed

- **Legacy and redundant files:**
  - Eliminated default Next.js app structure and boilerplate.
  - Removed legacy configuration files and unused package-lock.json in favor of pnpm-lock.yaml.

---
