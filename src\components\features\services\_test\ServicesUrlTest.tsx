"use client";

import { useState } from "react";
import { ServicesService } from "@/services";

export default function ServicesUrlTest() {
  const [testResult, setTestResult] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const testUrl = async () => {
    setIsLoading(true);
    try {
      // Test without extl_id
      console.log("Testing URL construction...");

      // This will help us see what URL is being constructed
      const result = await ServicesService.getServices({
        limit: 12,
        next: 1,
      });

      setTestResult(
        `Success! Got ${result.response.items?.length || 0} services`,
      );
    } catch (error: any) {
      console.error("URL Test Error:", error);
      setTestResult(`Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testUrlWithExtlId = async () => {
    setIsLoading(true);
    try {
      // Test with extl_id
      console.log("Testing URL construction with extl_id...");

      const result = await ServicesService.getServices({
        limit: 12,
        next: 1,
        extl_id: "test123",
      });

      setTestResult(
        `Success with extl_id! Got ${result.response.items?.length || 0} services`,
      );
    } catch (error: any) {
      console.error("URL Test Error:", error);
      setTestResult(`Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Services URL Test</h3>

      <div className="space-y-4">
        <div>
          <button
            onClick={testUrl}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isLoading ? "Testing..." : "Test URL (without extl_id)"}
          </button>
        </div>

        <div>
          <button
            onClick={testUrlWithExtlId}
            disabled={isLoading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {isLoading ? "Testing..." : "Test URL (with extl_id)"}
          </button>
        </div>

        {testResult && (
          <div className="mt-4 p-3 bg-gray-100 rounded">
            <strong>Result:</strong> {testResult}
          </div>
        )}

        <div className="mt-4 text-sm text-gray-600">
          <p>
            <strong>Expected URLs:</strong>
          </p>
          <p>
            • Without extl_id:{" "}
            <code>https://api-dev.afreeserv.com/services/all/12/1</code>
          </p>
          <p>
            • With extl_id:{" "}
            <code>
              https://api-dev.afreeserv.com/services/all/12/1?extl_id=test123
            </code>
          </p>
        </div>
      </div>
    </div>
  );
}
