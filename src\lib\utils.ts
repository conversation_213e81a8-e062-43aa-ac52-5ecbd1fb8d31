import { parseAsInteger, parseAsString, createLoader } from "nuqs/server";

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const globalQueryParams = {
  limit: parseAsInteger.withDefault(10),
  next: parseAsInteger.withDefault(1),
  extl_id: parseAsString,
};

export const getGlobalQueryParams = createLoader(globalQueryParams);
