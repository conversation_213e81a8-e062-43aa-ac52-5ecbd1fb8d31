import { DropdownMenu, DropdownMenuTrigger } from "@/components/ui";
import { DropdownTriggerButton } from "./account-dropdown-trigger-button";
import { UserAvatar } from "./account-dropdown-user-avatar";
import { DropdownMenuContent } from "@radix-ui/react-dropdown-menu";
import { DropdownContent } from "./account-dropdown-content";

export function AccountDropdown() {
  // Mock data for demonstration - replace with actual auth implementation
  const isSignedIn = false;
  const user = null;

  const handleSignOut = () => {
    console.log("Sign out functionality to be implemented");
  };

  const handleSignIn = () => {
    console.log("Sign in functionality to be implemented");
  };

  const getUserInitials = () => "U";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <DropdownTriggerButton>
          <UserAvatar initials={getUserInitials()} />
        </DropdownTriggerButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48 p-0">
        <DropdownContent
          isSignedIn={isSignedIn}
          onSignIn={handleSignIn}
          onSignOut={handleSignOut}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
