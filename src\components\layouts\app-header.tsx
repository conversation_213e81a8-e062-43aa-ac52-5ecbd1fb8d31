import { Separator } from "@radix-ui/react-separator";
import { AccountDropdown, LocaleSwitcher } from "../common";
import { SidebarTrigger } from "../ui/sidebar";

export function AppHeader() {
  return (
    <header className="bg-white z-30 sticky top-0 flex shrink-0 items-center justify-between gap-3 border-b border-secondary px-4.5 py-[5px]">
      {/* Left side - Sidebar trigger */}
      <div className="flex items-center gap-3">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="data-[orientation=vertical]:h-6 data-[orientation=vertical]:w-px bg-secondary"
        />
      </div>

      {/* Spacer to push right content to the end */}
      <div className="flex-1" />

      {/* Right side - Language switcher and account dropdown */}
      <div className="flex items-center gap-[80px]">
        <LocaleSwitcher />
        <div className="flex items-center gap-[12px]">
          <AccountDropdown />
        </div>
      </div>
    </header>
  );
}
