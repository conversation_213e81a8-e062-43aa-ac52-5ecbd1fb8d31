import { parseAsInteger, parseAsString, useQueryStates } from "nuqs";
import { useMemo } from "react";

export function useGlobalQueryParams() {
  const [searchParams, setSearchParams] = useQueryStates(
    {
      limit: parseAsInteger.withDefault(10),
      next: parseAsInteger.withDefault(1),
      extl_id: parseAsString,
      currency: parseAsString.withDefault("EUR"),
      display: parseAsString,
    },
    {
      history: "push",
      shallow: false,
      clearOnDefault: false, // Keep parameters in URL even if they match defaults
    },
  );

  const queryLength = useMemo(() => {
    // const {
    //   // categories
    // } = searchParams;
    const count = 0;

    // if (categories?.length) count++;

    return count;
  }, [searchParams]);

  return {
    searchParams,
    setSearchParams,
    queryLength,
  };
}
