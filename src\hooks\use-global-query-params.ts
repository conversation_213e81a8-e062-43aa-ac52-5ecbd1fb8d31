import {
  parseAsInteger,
  parseAsString,
  parseAsArrayOf,
  useQueryStates,
} from "nuqs";
import { useMemo } from "react";

export function useGlobalQueryParams() {
  const [searchParams, setSearchParams] = useQueryStates(
    {
      limit: parseAsInteger.withDefault(12),
      next: parseAsInteger.withDefault(1),
      extl_id: parseAsString,
      currency: parseAsString.withDefault("EUR"),
      display: parseAsString,
    },
    {
      history: "push",
      shallow: false,
    },
  );

  const queryLength = useMemo(() => {
    const {
      // categories
    } = searchParams;
    let count = 0;

    // if (categories?.length) count++;

    return count;
  }, [searchParams]);

  return {
    searchParams,
    setSearchParams,
    queryLength,
  };
}
