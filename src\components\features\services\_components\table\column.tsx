"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Service } from "@/features/services/services.schema";
import { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const t = useTranslations("Services");

  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case "published":
      case "publié":
        return {
          label: t("statusPublished"),
          className: "bg-green-100 text-green-800",
        };
      case "refused":
      case "refusé":
        return {
          label: t("statusRefused"),
          className: "bg-red-100 text-red-800",
        };
      case "hidden":
      case "masqué":
        return {
          label: t("statusHidden"),
          className: "bg-gray-100 text-gray-800",
        };
      case "deleted":
      case "supprimé":
        return {
          label: t("statusDeleted"),
          className: "bg-red-100 text-red-800",
        };
      case "pending":
      case "en attente":
        return {
          label: t("statusPending"),
          className: "bg-yellow-100 text-yellow-800",
        };
      case "disabled":
      case "désactivé":
        return {
          label: t("statusDisabled"),
          className: "bg-gray-100 text-gray-800",
        };
      default:
        return {
          label: t("statusUnknown"),
          className: "bg-gray-100 text-gray-800",
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}
    >
      {config.label}
    </span>
  );
};

// Action button component
const ActionButton = ({ serviceId }: { serviceId: number }) => {
  const router = useRouter();
  const t = useTranslations("Services");

  const handleViewMore = () => {
    // TODO: Implement view more functionality
    console.log("View more for service:", serviceId);
    router.push(`/services/details/${serviceId}`);
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleViewMore}
      className="w-[100px] h-[25px] px-2.5 py-2 text-xs font-medium text-primary border-secondary rounded-[5px] hover:bg-gray-50"
    >
      {t("viewMore")}
    </Button>
  );
};

// Date formatter
const formatDate = (dateString?: string | null) => {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");

  return `${day}/${month}/${year} - ${hours}:${minutes}`;
};

export const columns: ColumnDef<Service>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => {
      const t = useTranslations("Services");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
        >
          {t("serviceId")}
        </Button>
      );
    },
    cell: ({ row }) => {
      const id = row.getValue("id") as number;
      return id.toString();
    },
  },

  {
    accessorKey: "description",
    header: ({ column }) => {
      const t = useTranslations("Services");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
        >
          {t("serviceTitle")}
        </Button>
      );
    },
  },
  {
    accessorKey: "categories",
    header: ({ column }) => {
      const t = useTranslations("Services");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
        >
          {t("category")}
        </Button>
      );
    },
    cell: ({ row }) => {
      const categories = row.getValue("categories") as {
        id: number;
        intl_id: string;
        name: string;
      }[];
      return categories.map((cat) => cat.name).join(", ");
    },
  },
  {
    accessorKey: "status",
    enableColumnFilter: true,
    // filterFn will be set in the table configuration
    header: ({ column }) => {
      const t = useTranslations("Services");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
        >
          {t("status")}
        </Button>
      );
    },
    cell: ({ row }) => {
      const status = row.original.isPublished ? "published" : "draft";
      return <StatusBadge status={status} />;
    },
  },
  {
    accessorKey: "provider.id",
    header: ({ column }) => {
      const t = useTranslations("Services");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
        >
          {t("userId")}
        </Button>
      );
    },
    cell: ({ row }) => {
      const providerId = row.original.provider?.id;
      return providerId ? providerId.toString() : "N/A";
    },
  },
  {
    accessorKey: "provider",
    id: "username",
    header: ({ column }) => {
      const t = useTranslations("Services");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
        >
          {t("username")}
        </Button>
      );
    },
    cell: ({ row }) => {
      const provider = row.original.provider;
      if (!provider) return "N/A";

      const firstName = provider.firstName || "";
      const lastName = provider.lastName || "";
      return `${firstName} ${lastName}`.trim() || "N/A";
    },
  },
  {
    accessorKey: "createdAt",
    enableColumnFilter: true,
    // filterFn will be set in the table configuration
    header: ({ column }) => {
      const t = useTranslations("Services");
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
        >
          {t("createdAt")}
        </Button>
      );
    },
    cell: ({ row }) => {
      return formatDate(row.original.createdAt);
    },
  },
  {
    id: "actions",
    header: ({}) => {
      const t = useTranslations("Services");
      return t("action");
    },
    cell: ({ row }) => {
      return <ActionButton serviceId={row.original.id} />;
    },
  },
];
