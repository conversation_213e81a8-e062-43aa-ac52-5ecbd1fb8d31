import { ColumnDef } from "@tanstack/react-table";
import { TableColumnHeader } from "../table-column-header";

export const categoriesColumn: ColumnDef<Service> = {
  accessorKey: "categories",
  header: ({ column }) => (
    <TableColumnHeader column={column} translationKey="category" />
  ),
  cell: ({ row }) => {
    const categories = row.getValue("categories") as {
      id: number;
      intl_id: string;
      name: string;
    }[];
    return categories.map((cat) => cat.name).join(", ");
  },
};
