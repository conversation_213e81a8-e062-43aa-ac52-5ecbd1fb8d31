"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui";
import { IconChevronDown } from "@tabler/icons-react";
import { useGlobalQueryParams } from "@/hooks";

type CurrencyOption = {
  code: string;
  symbol: string;
  label: string;
};

const CURRENCY_OPTIONS: readonly CurrencyOption[] = [
  { code: "EUR", symbol: "€", label: "EUR" },
  { code: "XAF", symbol: "CFA", label: "XAF" },
  { code: "USD", symbol: "$", label: "USD" },
  { code: "GBP", symbol: "£", label: "GBP" },
] as const;

export function CurrencySwitcher() {
  const { searchParams, setSearchParams } = useGlobalQueryParams();

  return (
    <Select
      defaultValue={searchParams.currency}
      onValueChange={(value) =>
        setSearchParams((prev) => ({ ...prev, currency: value }))
      }
    >
      <SelectTrigger className="w-full !h-11.25 flex">
        <SelectValue
          placeholder=""
          className="flex justify-start text-start "
        />
        <IconChevronDown className="text-input" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {CURRENCY_OPTIONS.map((currency) => (
            <SelectItem
              key={currency.code}
              className="flex justify-start text-start"
              value={currency.code}
            >
              {currency.label}, {currency.symbol}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
