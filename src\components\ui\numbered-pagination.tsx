"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function NumberedPagination({
  currentPage,
  totalPages,
  onPageChange,
}: PaginationProps) {
  const getVisiblePages = () => {
    const pages = [];
    const maxVisible = 5;

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (currentPage > 3) {
        pages.push("...");
      }

      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      for (let i = start; i <= end; i++) {
        if (!pages.includes(i)) {
          pages.push(i);
        }
      }

      if (currentPage < totalPages - 2) {
        pages.push("...");
      }

      // Always show last page
      if (!pages.includes(totalPages)) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="flex mx-auto items-center justify-center gap-3 py-2.5 px-5 w-[353px] h-[60px]">
      {/* Spacer for alignment */}
      <div className="w-6 h-6" />

      {/* Page Numbers */}
      <div className="flex items-center gap-3">
        {visiblePages.map((page, index) => {
          if (page === "...") {
            return (
              <span
                key={`ellipsis-${index}`}
                className="text-[#373737] text-xs"
              >
                ...
              </span>
            );
          }

          const pageNumber = page as number;
          const isActive = pageNumber === currentPage;

          return (
            <Button
              key={pageNumber}
              variant="ghost"
              size="sm"
              onClick={() => onPageChange(pageNumber)}
              className={`
                w-6 h-6 p-0 text-xs font-medium rounded-[5px] border
                ${
                  isActive
                    ? "bg-[#D4A01F] text-white border-[#D4A01F] hover:bg-[#D4A01F]"
                    : "bg-white text-[#373737] border-[#EDEDED] hover:bg-gray-50"
                }
              `}
            >
              {pageNumber}
            </Button>
          );
        })}
      </div>

      {/* Next Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
        className="w-6 h-6 p-0 bg-white border border-[#EDEDED] rounded-[5px] disabled:opacity-50"
      >
        <ChevronRight className="w-3 h-3 text-[#373737]" />
      </Button>
    </div>
  );
}
