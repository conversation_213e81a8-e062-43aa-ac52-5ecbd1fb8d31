import React from "react";
import { IconArrowLeft, IconX } from "@tabler/icons-react";
import { AnimatePresence, motion } from "motion/react";
import { cn } from "@/lib";

interface GlobalSheetProps
  extends Omit<React.ComponentProps<"div">, "children"> {
  children: React.ReactNode;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  fade?: "up" | "down" | "right" | "left";
  header?: string;
  closeAsBackBtn?: boolean;
  showDefaultCloseBtn?: boolean;
}

export const GlobalSheet = ({
  className,
  children,
  isOpen,
  setIsOpen,
  fade = "up",
  header,
  closeAsBackBtn = false,
  showDefaultCloseBtn = true,
}: GlobalSheetProps) => {
  // Map fade prop to motion values
  const getAnimation = (direction: "up" | "down" | "right" | "left") => {
    switch (direction) {
      case "up":
        return {
          initial: { y: "100%", opacity: 0 },
          animate: { y: 0, opacity: 1 },
          exit: { y: "100%", opacity: 0 },
        };
      case "down":
        return {
          initial: { y: "-100%", opacity: 0 },
          animate: { y: 0, opacity: 1 },
          exit: { y: "-100%", opacity: 0 },
        };
      case "left":
        return {
          initial: { x: "-100%", opacity: 0 },
          animate: { x: 0, opacity: 1 },
          exit: { x: "-100%", opacity: 0 },
        };
      case "right":
      default:
        return {
          initial: { x: "100%", opacity: 0 },
          animate: { x: 0, opacity: 1 },
          exit: { x: "100%", opacity: 0 },
        };
    }
  };
  const { initial, animate, exit } = getAnimation(fade);
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
            className="fixed inset-0 bg-black/20 z-10"
          />
          {/* Sheet */}
          <motion.div
            initial={initial}
            animate={animate}
            exit={exit}
            transition={{
              duration: 0.5,
              ease: "easeInOut",
            }}
            className={cn(
              "fixed h-full inset-y-0 right-0 z-20 flex flex-col",
              className,
            )}
          >
            <div className="w-full md:w-[400px] h-full bg-white shadow-lg relative">
              <div className="sticky w-full top-0 z-30 px-5 py-2.5 flex justify-between items-center bg-white border-b border-input">
                <div className="">
                  {closeAsBackBtn && (
                    <button
                      onClick={() => setIsOpen(false)}
                      className="cursor-pointer text-main-black"
                      aria-label="Close sheet"
                    >
                      <IconArrowLeft size={20} />
                    </button>
                  )}
                </div>
                <h6 className="text-main-black text-sm uppercase">{header}</h6>
                <div className="">
                  {showDefaultCloseBtn && (
                    <button
                      onClick={() => setIsOpen(false)}
                      className="cursor-pointer text-main-black"
                      aria-label="Close sheet"
                    >
                      <IconX size={20} />
                    </button>
                  )}
                </div>
              </div>
              <div className="h-full overflow-y-auto">{children}</div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
