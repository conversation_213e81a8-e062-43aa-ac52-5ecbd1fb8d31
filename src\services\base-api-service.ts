import { z } from "zod";

export type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

export interface ApiRequestOptions {
  method?: HttpMethod;
  body?: Record<string, string | number | boolean | string[]> | FormData;
  headers?: Record<string, string>;
  authenticated?: boolean;
  query?: Record<string, string | number>;
  tag?: string;
}

export class ApiError extends Error {
  status: number;
  details?: unknown;

  constructor(message: string, status: number, details?: unknown) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.details = details;
  }
}

export abstract class BaseApiService {
  protected defaultHeaders: Record<string, string>;
  protected baseUrl: string;

  constructor(baseUrl?: string, defaultHeaders: Record<string, string> = {}) {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_BASE_API_URL || "";
    this.defaultHeaders = {
      "Content-Type": "application/json",
      ...defaultHeaders,
    };
  }

  /**
   * Get the access token from localStorage (client-side)
   * @returns The access token or null if not found
   */
  protected getAuthToken(): string | null {
    if (typeof window === "undefined") {
      return null; // Server-side, no token available
    }
    return localStorage.getItem("access_token");
  }

  /**
   * Set the access token in localStorage (client-side)
   */
  public setAccessToken(token: string): void {
    if (typeof window !== "undefined") {
      localStorage.setItem("access_token", token);
    }
  }

  /**
   * Remove the access token from localStorage (client-side)
   */
  public clearAccessToken(): void {
    if (typeof window !== "undefined") {
      localStorage.removeItem("access_token");
    }
  }

  /**
   * Construct full URL with query parameters
   */
  private buildUrl(
    endpoint: string,
    query?: Record<string, string | number>,
  ): string {
    const url = new URL(`${this.baseUrl}${endpoint.replace(/^\//, "")}`);

    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        if (value != null) {
          url.searchParams.append(key, value.toString());
        }
      });
    }

    return url.toString();
  }

  /**
   * Prepare headers for the request
   */
  private prepareHeaders(options: ApiRequestOptions): Headers {
    const headers = new Headers(this.defaultHeaders);

    // Add custom headers
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        headers.set(key, value);
      });
    }

    // Handle authentication
    if (options.authenticated) {
      const token = this.getAuthToken();
      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }
    }

    return headers;
  }

  /**
   * Process and validate the API response
   */
  private async processResponse<T>(
    response: Response,
    responseSchema?: z.ZodType<T>,
  ): Promise<ApiResponse<T>> {
    if (!response.ok) {
      if (response.status === 401) {
        // Clear token on unauthorized
        this.clearAccessToken();
        // In client-side, we can redirect using window.location
        if (typeof window !== "undefined") {
          window.location.href = "/logout";
        }
        throw new ApiError("Unauthorized", 401);
      }
      throw new ApiError(
        `HTTP error! status: ${response.status}`,
        response.status,
      );
    }

    const responseData = await response.json();
    if (responseSchema) {
      const validationResult = responseSchema.safeParse(responseData);
      if (!validationResult.success) {
        throw new ApiError(
          "Response Validation Failed",
          422,
          validationResult.error,
        );
      }
    }

    return {
      status: response.status,
      response: responseData,
    };
  }

  /**
   * Make an API request with comprehensive error handling
   */
  protected async request<T>(
    endpoint: string,
    options: ApiRequestOptions = {},
    responseSchema?: z.ZodType<T>,
  ): Promise<ApiResponse<T>> {
    const { method = "GET", body, query } = options;
    const url = this.buildUrl(endpoint, query);
    const headers = this.prepareHeaders(options);

    const fetchOptions: RequestInit = {
      method,
      headers,
      ...(body && {
        body: body instanceof FormData ? body : JSON.stringify(body),
      }),
    };

    const response = await fetch(url, fetchOptions);
    return this.processResponse(response, responseSchema);
  }
}
