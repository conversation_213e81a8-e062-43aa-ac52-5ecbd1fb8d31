{"HomePage": {"title": "Hello World!", "about": "About the about page"}, "LocaleSwitcher": {"de": "German", "en": "English", "fr": "French", "code": "Code", "label": "Language"}, "FormsValidation": {"input_field_required": "Input field required", "select_field_required": "Select field required", "textarea_field_required": "Select field required"}, "Auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "signInTitle": "Sign In - Back Office", "signInDescription": "Sign in to access your back office dashboard", "signUpTitle": "Sign Up - Back Office", "signUpDescription": "Create an account to access the back office system", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "rememberMe": "Remember me", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "createAccount": "Create Account", "welcomeBack": "Welcome back!", "createYourAccount": "Create your account", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "invalidCredentials": "Invalid email or password", "accountCreated": "Account created successfully", "passwordReset": "Password reset email sent", "sessionExpired": "Your session has expired. Please sign in again."}, "Navigation": {"appName": "Application", "home": "Home", "about": "About", "services": "Services", "servicesList": "Services List", "servicesDetails": "Services Details", "support": "Support", "ticketsList": "Tickets List", "ticketsDetails": "Tickets Details", "settings": "Settings", "adminsList": "Administrators List", "adminsDetails": "Administrators Details", "newAdmin": "New Administrator", "contact": "Contact", "dashboard": "Dashboard", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "Header": {"language": "English, USD", "myAccount": "My Account", "signIn": "Sign In", "signOut": "Sign Out", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "Services": {"title": "Services", "subtitle": "Manage and view all services in the system", "pageDescription": "Showing {current} of {total} services", "name": "Name", "description": "Description", "provider": "Provider", "price": "Price", "status": "Status", "categories": "Categories", "published": "Published", "draft": "Draft", "noServices": "No services found"}}