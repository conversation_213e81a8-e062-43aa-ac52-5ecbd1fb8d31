import Link from "next/link";
import {
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

interface SimpleNavigationItemProps {
  item: NavigationItem;
  isSectionActive: (name: string, href?: string) => boolean;
  t: (key: string) => string;
}

export const SimpleNavigationItem = ({
  item,
  isSectionActive,
  t,
}: SimpleNavigationItemProps) => {
  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        className={`w-full justify-between ${
          isSectionActive(item.titleKey)
            ? "bg-sidebar-primary text-sidebar-primary-foreground"
            : ""
        }`}
      >
        <Link href={String(item.href)}>
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center size-6 rounded-lg">
              <item.icon className="size-4" />
            </div>
            <span>{t(item.titleKey)}</span>
          </div>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
};
