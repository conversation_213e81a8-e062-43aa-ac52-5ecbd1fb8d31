"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

interface TableActionButtonProps {
  serviceId: number;
}

export function TableActionButton({ serviceId }: TableActionButtonProps) {
  const router = useRouter();
  const t = useTranslations("Services");

  const handleViewMore = () => {
    // TODO: Implement view more functionality
    console.log("View more for service:", serviceId);
    router.push(`/services/details/${serviceId}`);
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleViewMore}
      className="w-[80px] h-[26px] px-3 py-2 text-xs font-medium text-primary border-secondary rounded-[5px] hover:bg-gray-50"
    >
      {t("viewMore")}
    </Button>
  );
}
