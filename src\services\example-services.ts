import { BaseApiService } from "./base-service";

export class ExampleService extends BaseApiService {
  // This is basic use example of BaseApiService
  private BASE_AUTH_URL = "users/";
  constructor() {
    super();
    this.getByExternalId = this.getByExternalId.bind(this);
  }

  async getByExternalId(extl_id: string) {
    return await this.request(
      `${this.BASE_AUTH_URL}extl_id/${extl_id}`,
      {
        method: "GET",
        tag: `user-extl_id-${extl_id}`,
      },
    );
  }
}
