import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Table } from "@tanstack/react-table";
import { useTranslations } from "next-intl";

interface TableDateFilterProps<TData> {
  table: Table<TData>;
}

export function TableDateFilter<TData>({ table }: TableDateFilterProps<TData>) {
  const t = useTranslations("Services");

  return (
    <div className="w-[200px] h-[35px]">
      <Select
        onValueChange={(value) =>
          table.getColumn("createdAt")?.setFilterValue(value)
        }
        value={
          (table.getColumn("createdAt")?.getFilterValue() as string) ?? "all"
        }
      >
        <SelectTrigger className="w-full h-full border-secondary rounded-[10px] text-xs text-[#373737] px-2.5 py-1.5">
          <SelectValue placeholder={t("dateThisYear")} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t("dateAll")}</SelectItem>
          <SelectItem value="this-year">{t("thisYear")}</SelectItem>
          <SelectItem value="last-year">{t("lastYear")}</SelectItem>
          <SelectItem value="this-month">{t("thisMonth")}</SelectItem>
          <SelectItem value="last-month">{t("lastMonth")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
