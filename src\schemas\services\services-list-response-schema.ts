import { z } from "zod";
import { ServiceSchema } from "./service-schema";
import { SupportSchema } from "./support-schema";
import { PaginatedResponseSchema } from "./pagination-schema";

// Services List Response Schema
export const ServicesListResponseSchema = PaginatedResponseSchema.extend({
  items: z.array(ServiceSchema),
});

// Service Detail Response Schema
export const ServiceDetailResponseSchema = ServiceSchema;

// User Services Response Schema
export const UserServicesResponseSchema = z.array(ServiceSchema);

// Support Detail Response Schema
export const SupportDetailResponseSchema = SupportSchema;

// Supports List Response Schema
export const SupportsListResponseSchema = PaginatedResponseSchema.extend({
  items: z.array(SupportSchema),
});
