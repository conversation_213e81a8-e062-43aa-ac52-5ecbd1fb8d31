import Image from "next/image";
import Link from "next/link";
import {
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export const SidebarLogo = () => {
  return (
    <SidebarHeader className="p-1.5 border-b">
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton className="hover:bg-transparent" size="lg" asChild>
            <Link href="/">
              <div className="flex items-center gap-3">
                <div className="relative w-[200] h-[80]">
                  <Image
                    src="/logo.jpg"
                    alt="Logo"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
  );
};
