import { useGlobalQueryParams } from "@/hooks/use-global-query-params";
import { Table } from "@tanstack/react-table";
import { PaginationButton } from "./pagination-button";
import { PaginationNextButton } from "./pagination-next-button";
import { getVisiblePages } from "./pagination-utils";

interface TablePaginationProps<TData> {
  table: Table<TData>;
}

export function TablePagination<TData>({ table }: TablePaginationProps<TData>) {
  const { searchParams, setSearchParams } = useGlobalQueryParams();

  if (table.getPageCount() <= 1) {
    return null;
  }

  const currentPage = searchParams.next || 1;
  const totalPages = table.getPageCount();

  const handlePageChange = (page: number) => {
    setSearchParams({ ...searchParams, next: page });
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setSearchParams({ ...searchParams, next: currentPage + 1 });
    }
  };

  return (
    <div className="flex mx-auto items-center justify-center gap-3 py-2.5 px-5 w-[353px] h-[60px]">
      {/* Spacer for alignment */}
      <div className="w-6 h-6" />

      {/* Page Numbers */}
      <div className="flex items-center gap-3">
        {getVisiblePages(currentPage, totalPages).map((page, index) => {
          if (page === "...") {
            return (
              <span
                key={`ellipsis-${index}`}
                className="text-[#373737] text-xs"
              >
                ...
              </span>
            );
          }

          const pageNumber = page as number;
          const isActive = pageNumber === currentPage;

          return (
            <PaginationButton
              key={pageNumber}
              pageNumber={pageNumber}
              isActive={isActive}
              onClick={() => handlePageChange(pageNumber)}
            />
          );
        })}
      </div>

      {/* Next Button */}
      <PaginationNextButton
        onClick={handleNextPage}
        disabled={currentPage >= totalPages}
      />
    </div>
  );
}
