import { Table } from "@tanstack/react-table";
import { PaginationButton } from "./pagination-button";
import { PaginationNextButton } from "./pagination-next-button";
import { getVisiblePages } from "./pagination-utils";

interface TablePaginationProps<TData> {
  table: Table<TData>;
}

export function TablePagination<TData>({ table }: TablePaginationProps<TData>) {
  if (table.getPageCount() <= 1) {
    return null;
  }

  return (
    <div className="flex mx-auto items-center justify-center gap-3 py-2.5 px-5 w-[353px] h-[60px]">
      {/* Spacer for alignment */}
      <div className="w-6 h-6" />

      {/* Page Numbers */}
      <div className="flex items-center gap-3">
        {getVisiblePages(
          table.getState().pagination.pageIndex + 1,
          table.getPageCount()
        ).map((page, index) => {
          if (page === "...") {
            return (
              <span
                key={`ellipsis-${index}`}
                className="text-[#373737] text-xs"
              >
                ...
              </span>
            );
          }

          const pageNumber = page as number;
          const isActive =
            pageNumber === table.getState().pagination.pageIndex + 1;

          return (
            <PaginationButton
              key={pageNumber}
              pageNumber={pageNumber}
              isActive={isActive}
              onClick={() => table.setPageIndex(pageNumber - 1)}
            />
          );
        })}
      </div>

      {/* Next Button */}
      <PaginationNextButton
        onClick={() => table.nextPage()}
        disabled={!table.getCanNextPage()}
      />
    </div>
  );
}
