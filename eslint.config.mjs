import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    plugins: {
      react: (await import("eslint-plugin-react")).default,
    },
    rules: {
      // Enforce maximum lines per function/component
      "max-lines-per-function": [
        "warn",
        {
          max: 200,
          skipBlankLines: true,
          skipComments: true,
          IIFEs: true,
        },
      ],

      // Limit file length
      "max-lines": [
        "warn",
        {
          max: 300,
          skipBlankLines: true,
          skipComments: true,
        },
      ],

      // Limit complexity
      complexity: ["warn", { max: 10 }],

      // Limit nested depth
      "max-depth": ["warn", { max: 4 }],

      // Limit number of function parameters
      "max-params": ["warn", { max: 5 }],

      // React: Format props (one per line when multiline)
      "react/jsx-max-props-per-line": [
        "warn",
        {
          maximum: 1,
          when: "multiline",
        },
      ],

      // React: Limit props indentation and formatting
      "react/jsx-first-prop-new-line": ["warn", "multiline-multiprop"],
      "react/jsx-closing-bracket-location": ["warn", "tag-aligned"],

      // Custom rule to warn about too many props (manual enforcement)
      "prefer-const": "warn", // This is a placeholder - see alternative approaches below
    },
  },
];

export default eslintConfig;
