import { But<PERSON> } from "@/components/ui/button";
import { Column } from "@tanstack/react-table";
import { useTranslations } from "next-intl";

interface TableColumnHeaderProps<TData> {
  column: Column<TData, unknown>;
  translationKey: string;
}

export function TableColumnHeader<TData>({ 
  column, 
  translationKey 
}: TableColumnHeaderProps<TData>) {
  const t = useTranslations("Services");

  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      className="h-auto p-0 font-medium text-foreground hover:bg-transparent"
    >
      {t(translationKey)}
    </Button>
  );
}
