import { Skeleton } from "@/components/ui/skeleton";

export default function ServicesTableSkeleton() {
  return (
    <div className="bg-white rounded-[10px] border border-secondary overflow-hidden">
      <div className="p-1">
        <div className="px-2.5 py-3">
          <div className="flex justify-between items-center gap-14">
            <Skeleton className="h-9 min-w-[200px] max-w-[384px]" />
            <div className="flex items-center gap-5">
              <Skeleton className="h-[35px] w-[200px]" />
              <Skeleton className="h-[35px] w-[200px]" />
              <Skeleton className="h-[35px] w-[200px]" />
            </div>
          </div>
        </div>
      </div>

      <div className="border-t border-secondary">
        {/* Table Header */}
        <div className="bg-secondary">
          <div className="grid grid-cols-8 gap-4 px-4 py-3">
            {Array.from({ length: 8 }).map((_, i) => (
              <Skeleton key={i} className="h-4" />
            ))}
          </div>
        </div>

        {/* Table Body */}
        {Array.from({ length: 5 }).map((_, rowIndex) => (
          <div key={rowIndex} className="border-b border-secondary">
            <div className="grid grid-cols-8 gap-4 px-4 py-4">
              {Array.from({ length: 8 }).map((_, colIndex) => (
                <Skeleton key={colIndex} className="h-4" />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination Skeleton */}
      <div className="flex mx-auto items-center justify-center gap-3 py-2.5 px-5 w-[353px] h-[60px]">
        <Skeleton className="w-6 h-6" />
        <div className="flex items-center gap-3">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="w-6 h-6 rounded-[5px]" />
          ))}
        </div>
        <Skeleton className="w-6 h-6" />
      </div>
    </div>
  );
}
