import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGlobalQueryParams } from "@/hooks/use-global-query-params";
import { ChevronDownIcon } from "lucide-react";
import { useTranslations } from "next-intl";

export function TablePageSizeFilter() {
  const t = useTranslations("Services");
  const { searchParams, setSearchParams } = useGlobalQueryParams();

  const handlePageSizeChange = (value: string) => {
    setSearchParams({
      ...searchParams,
      limit: Number(value),
      next: 1, // Reset to first page when changing page size
    });
  };

  return (
    <div>
      <Select
        onValueChange={handlePageSizeChange}
        value={(searchParams.limit || 20).toString()}
      >
        <SelectTrigger className="w-full border-input rounded-md bg-background focus:ring-2 focus:ring-ring focus:ring-offset-2">
          <SelectValue placeholder={t("display20")} />
          <ChevronDownIcon className="text-input" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="10">{t("display10")}</SelectItem>
          <SelectItem value="20">{t("display20")}</SelectItem>
          <SelectItem value="50">{t("display50")}</SelectItem>
          <SelectItem value="100">{t("display100")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
