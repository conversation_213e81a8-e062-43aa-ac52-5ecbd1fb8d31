import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGlobalQueryParams } from "@/hooks/use-global-query-params";
import { Table } from "@tanstack/react-table";
import { ChevronDownIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface TablePageSizeFilterProps<TData> {
  table: Table<TData>;
}

export function TablePageSizeFilter<TData>({
  table,
}: TablePageSizeFilterProps<TData>) {
  const t = useTranslations("Services");
  const { searchParams, setSearchParams } = useGlobalQueryParams();

  const handlePageSizeChange = (value: string) => {
    setSearchParams({
      ...searchParams,
      limit: Number(value),
      next: 1, // Reset to first page when changing page size
    });
  };

  return (
    <div className="w-[200px] h-[35px]">
      <Select
        onValueChange={handlePageSizeChange}
        value={(searchParams.limit || 12).toString()}
      >
        <SelectTrigger className="w-full h-full  rounded-md border-input rounded-[10px] text-xs text-input px-2.5 py-1.5">
          <SelectValue placeholder={t("display20")} />
          <ChevronDownIcon className="text-input" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="10">{t("display10")}</SelectItem>
          <SelectItem value="20">{t("display20")}</SelectItem>
          <SelectItem value="50">{t("display50")}</SelectItem>
          <SelectItem value="100">{t("display100")}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
