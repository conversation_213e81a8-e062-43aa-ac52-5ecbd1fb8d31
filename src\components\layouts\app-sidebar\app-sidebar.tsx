"use client";

import { useTranslations } from "next-intl";
import * as React from "react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarMenu,
  SidebarRail,
} from "@/components/ui/sidebar";

import { SidebarLogo } from "./sidebar-header";
import { NavigationItem } from "./sidebar-navigation";
import { navigationData } from "./navigation-data";
import { usePathUtils } from "./sidebar-utils";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const t = useTranslations("Navigation");
  const { isLinkActive, isSectionActive } = usePathUtils();

  return (
    <Sidebar {...props}>
      <SidebarLogo />
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu>
            {navigationData.navMain.map((item) => (
              <NavigationItem
                key={item.titleKey}
                item={item}
                isSectionActive={isSectionActive}
                isLinkActive={isLinkActive}
                t={t}
              />
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
