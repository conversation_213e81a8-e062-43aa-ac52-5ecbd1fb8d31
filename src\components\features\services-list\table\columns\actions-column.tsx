import { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { TableActionButton } from "../table-action-button";

export const actionsColumn: ColumnDef<Service> = {
  id: "actions",
  header: () => {
    const t = useTranslations("Services");
    return t("action");
  },
  cell: ({ row }) => {
    return <TableActionButton serviceId={row.original.id} />;
  },
};
