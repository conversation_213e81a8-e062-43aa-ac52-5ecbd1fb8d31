import { Table } from "@tanstack/react-table";
import { TableDateFilter } from "./table-date-filter";
import { TablePageSizeFilter } from "./table-page-size-filter";
import { TableSearchFilter } from "./table-search-filter";
import { TableStatusFilter } from "./table-status-filter";
import React from "react";

interface TableFiltersProps<TData> {
  table: Table<TData>;
}

export function TableFilters<TData>({ table }: TableFiltersProps<TData>) {
  return (
    <div className="p-1">
      <div className="px-2.5 py-3">
        <div className="flex justify-between items-center gap-14">
          <TableSearchFilter table={table} />

          <div className="flex items-center gap-4.5">
            <TableDateFilter table={table} />
            <TableStatusFilter table={table} />
            <TablePageSizeFilter table={table} />
          </div>
        </div>
      </div>
    </div>
  );
}
